package com.fixguru.utils;

import lombok.extern.slf4j.Slf4j;

import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.util.HashMap;
import java.util.Map;

/**
 * SMTP连接诊断工具类
 * 用于诊断SMTP连接问题，提供详细的连接测试和错误分析
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public final class SmtpDiagnosticUtils {

    private SmtpDiagnosticUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 诊断SMTP连接
     *
     * @param host SMTP服务器地址
     * @param port SMTP端口
     * @return 诊断结果
     */
    public static Map<String, Object> diagnoseSmtpConnection(String host, int port) {
        Map<String, Object> result = new HashMap<>();
        result.put("host", host);
        result.put("port", port);
        result.put("timestamp", System.currentTimeMillis());

        log.info("开始SMTP连接诊断: {}:{}", host, port);

        // 1. 基础网络连接测试
        Map<String, Object> networkTest = testNetworkConnection(host, port);
        result.put("networkConnection", networkTest);

        // 2. SMTP协议握手测试
        Map<String, Object> smtpTest = testSmtpHandshake(host, port);
        result.put("smtpHandshake", smtpTest);

        // 3. STARTTLS支持测试
        Map<String, Object> tlsTest = testStartTlsSupport(host, port);
        result.put("startTlsSupport", tlsTest);

        // 4. 生成诊断建议
        String recommendation = generateRecommendation(networkTest, smtpTest, tlsTest);
        result.put("recommendation", recommendation);

        boolean overallSuccess = (Boolean) networkTest.get("success") && 
                                (Boolean) smtpTest.get("success");
        result.put("overallSuccess", overallSuccess);

        log.info("SMTP连接诊断完成: {}:{}, 结果: {}", host, port, overallSuccess ? "成功" : "失败");
        return result;
    }

    /**
     * 测试基础网络连接
     */
    private static Map<String, Object> testNetworkConnection(String host, int port) {
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();

        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), 10000); // 10秒超时
            long responseTime = System.currentTimeMillis() - startTime;

            result.put("success", true);
            result.put("responseTime", responseTime + "ms");
            result.put("message", "网络连接成功");

            log.debug("网络连接测试成功: {}:{}, 响应时间: {}ms", host, port, responseTime);

        } catch (SocketTimeoutException e) {
            result.put("success", false);
            result.put("error", "连接超时");
            result.put("message", "无法在10秒内连接到SMTP服务器，可能是网络问题或防火墙阻止");
            log.warn("网络连接超时: {}:{}", host, port);

        } catch (IOException e) {
            result.put("success", false);
            result.put("error", e.getClass().getSimpleName());
            result.put("message", "网络连接失败: " + e.getMessage());
            log.warn("网络连接失败: {}:{}, 错误: {}", host, port, e.getMessage());
        }

        return result;
    }

    /**
     * 测试SMTP协议握手
     */
    private static Map<String, Object> testSmtpHandshake(String host, int port) {
        Map<String, Object> result = new HashMap<>();

        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), 10000);
            socket.setSoTimeout(10000); // 读取超时10秒

            BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
            PrintWriter writer = new PrintWriter(socket.getOutputStream(), true);

            // 读取服务器欢迎消息
            String welcomeMessage = reader.readLine();
            log.debug("SMTP服务器欢迎消息: {}", welcomeMessage);

            if (welcomeMessage != null && welcomeMessage.startsWith("220")) {
                result.put("success", true);
                result.put("welcomeMessage", welcomeMessage);
                result.put("message", "SMTP握手成功");

                // 发送EHLO命令测试扩展功能
                writer.println("EHLO localhost");
                StringBuilder ehloResponse = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    ehloResponse.append(line).append("\n");
                    if (!line.startsWith("250-")) {
                        break;
                    }
                }
                result.put("ehloResponse", ehloResponse.toString());

            } else {
                result.put("success", false);
                result.put("welcomeMessage", welcomeMessage);
                result.put("message", "SMTP服务器返回异常欢迎消息");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getClass().getSimpleName());
            result.put("message", "SMTP握手失败: " + e.getMessage());
            log.warn("SMTP握手失败: {}:{}, 错误: {}", host, port, e.getMessage());
        }

        return result;
    }

    /**
     * 测试STARTTLS支持
     */
    private static Map<String, Object> testStartTlsSupport(String host, int port) {
        Map<String, Object> result = new HashMap<>();

        try {
            SSLSocketFactory factory = (SSLSocketFactory) SSLSocketFactory.getDefault();
            try (SSLSocket sslSocket = (SSLSocket) factory.createSocket(host, port)) {
                sslSocket.setSoTimeout(10000);
                sslSocket.startHandshake();

                result.put("success", true);
                result.put("supportedProtocols", String.join(", ", sslSocket.getSupportedProtocols()));
                result.put("enabledProtocols", String.join(", ", sslSocket.getEnabledProtocols()));
                result.put("message", "SSL/TLS连接成功");

                log.debug("SSL/TLS连接成功: {}:{}", host, port);

            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getClass().getSimpleName());
            result.put("message", "SSL/TLS连接失败: " + e.getMessage());
            log.warn("SSL/TLS连接失败: {}:{}, 错误: {}", host, port, e.getMessage());
        }

        return result;
    }

    /**
     * 生成诊断建议
     */
    private static String generateRecommendation(Map<String, Object> networkTest, 
                                               Map<String, Object> smtpTest, 
                                               Map<String, Object> tlsTest) {
        StringBuilder recommendation = new StringBuilder();

        if (!(Boolean) networkTest.get("success")) {
            recommendation.append("1. 检查网络连接和防火墙设置\n");
            recommendation.append("2. 确认SMTP服务器地址和端口正确\n");
            recommendation.append("3. 尝试使用其他网络环境测试\n");
        }

        if (!(Boolean) smtpTest.get("success")) {
            recommendation.append("4. 检查SMTP服务器是否正常运行\n");
            recommendation.append("5. 确认SMTP服务配置正确\n");
        }

        if (!(Boolean) tlsTest.get("success")) {
            recommendation.append("6. 检查SSL/TLS配置\n");
            recommendation.append("7. 尝试使用不同的TLS版本\n");
        }

        if (recommendation.length() == 0) {
            recommendation.append("连接诊断正常，请检查SMTP认证配置（用户名/密码）");
        }

        return recommendation.toString();
    }

    /**
     * 快速测试SMTP连接
     * 简化版本，只测试基础连接
     */
    public static boolean quickTestSmtpConnection(String host, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), 5000);
            return true;
        } catch (Exception e) {
            log.debug("SMTP快速连接测试失败: {}:{}, 错误: {}", host, port, e.getMessage());
            return false;
        }
    }
}
