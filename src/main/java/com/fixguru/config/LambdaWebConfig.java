package com.fixguru.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.filter.CharacterEncodingFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * Lambda环境专用的Web配置
 * 整合了CORS、静态资源、字符编码、拦截器等所有Web相关配置
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
@Profile("lambda")
@RequiredArgsConstructor
public class LambdaWebConfig implements WebMvcConfigurer {

    private final RequestInterceptor requestInterceptor;
    private final AuthenticationInterceptor authenticationInterceptor;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Knife4j静态资源 - Lambda环境优化
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/")
                .setCachePeriod(0)
                .resourceChain(false);

        registry.addResourceHandler("/doc.html")
                .addResourceLocations("classpath:/META-INF/resources/")
                .setCachePeriod(0)
                .resourceChain(false);

        // Swagger UI资源
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/swagger-ui/")
                .setCachePeriod(0)
                .resourceChain(false);

        // API文档资源
        registry.addResourceHandler("/v3/api-docs/**")
                .addResourceLocations("classpath:/META-INF/resources/")
                .setCachePeriod(0)
                .resourceChain(false);

        // 通用静态资源
        registry.addResourceHandler("/favicon.ico")
                .addResourceLocations("classpath:/META-INF/resources/", "classpath:/static/")
                .setCachePeriod(0)
                .resourceChain(false);

        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(0)
                .resourceChain(false);
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // Lambda环境CORS配置
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD")
                .allowedHeaders("*")
                .allowCredentials(false) // Lambda环境中设置为false
                .maxAge(3600);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 请求追踪拦截器
        registry.addInterceptor(requestInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(
                    "/v1/health/**",      // 排除所有健康检查接口
                    "/actuator/**",       // 排除监控端点
                    "/doc.html",          // 排除API文档
                    "/swagger-ui/**",     // 排除Swagger UI
                    "/v3/api-docs/**",    // 排除API文档
                    "/webjars/**",        // 排除静态资源
                    "/favicon.ico",       // 排除图标
                    "/static/**"          // 排除静态资源
                );

        // 认证拦截器
        registry.addInterceptor(authenticationInterceptor)
                .addPathPatterns("/v1/**") // 拦截所有API请求
                .excludePathPatterns(
                    "/v1/health/**",      // 排除所有健康检查接口
                    // 排除不需要token的用户接口
                    "/v1/users/register", // 用户注册
                    "/v1/users/login",    // 用户登录
                    "/v1/users/forgot-password", // 忘记密码
                    "/v1/users/reset-password",  // 重置密码
                    "/v1/users/refresh-token",   // 刷新令牌
                    "/v1/users/activate", // 激活用户账户
                    "/v1/users/check/**"         // 检查接口（邮箱等）
                );
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 配置UTF-8字符串消息转换器
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        stringConverter.setWriteAcceptCharset(false);
        converters.add(0, stringConverter);
    }

    @Bean
    public StringHttpMessageConverter stringHttpMessageConverter() {
        return new StringHttpMessageConverter(StandardCharsets.UTF_8);
    }

    @Bean
    public CharacterEncodingFilter characterEncodingFilter() {
        CharacterEncodingFilter filter = new CharacterEncodingFilter();
        filter.setEncoding("UTF-8");
        filter.setForceEncoding(true);
        filter.setForceRequestEncoding(true);
        filter.setForceResponseEncoding(true);
        return filter;
    }


}
