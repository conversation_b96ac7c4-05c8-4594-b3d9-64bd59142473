package com.fixguru.aws.dynamodb;

import com.fixguru.constants.DynamoDBConstants;
import com.fixguru.domain.UserDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.*;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;

import java.util.List;
import java.util.Optional;

/**
 * GSI查询服务
 * 演示如何使用全局二级索引进行查询
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GSIQueryService {

    private final DynamoDbEnhancedClient enhancedClient;
    private final String tablePrefix;

    /**
     * 根据用户名查询用户（使用GSI）
     *
     * @param userName 用户名
     * @return 用户信息
     */
    public Optional<UserDO> findUserByUserName(String userName) {
        return findUserByGSI(userName, DynamoDBConstants.USERNAME_GSI_INDEX, DynamoDBConstants.USERNAME_FIELD);
    }

    /**
     * 根据邮箱查询用户（使用GSI）
     *
     * @param email 邮箱
     * @return 用户信息
     */
    public Optional<UserDO> findUserByEmail(String email) {
        return findUserByGSI(email, DynamoDBConstants.EMAIL_GSI_INDEX, DynamoDBConstants.EMAIL_FIELD);
    }

    /**
     * 通用GSI查询方法
     * 使用模板方法模式，消除重复代码
     *
     * @param queryValue 查询值
     * @param indexName GSI索引名称
     * @param fieldName 字段名称（用于日志）
     * @return 用户信息
     */
    private Optional<UserDO> findUserByGSI(String queryValue, String indexName, String fieldName) {
        long startTime = System.currentTimeMillis();
        try {
            // 获取用户表
            DynamoDbTable<UserDO> table = getUserTable();

            // 获取指定的GSI
            DynamoDbIndex<UserDO> index = table.index(indexName);

            // 构建查询条件
            QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(queryValue).build());

            // 执行查询，获取所有匹配的用户
            List<UserDO> users = index.query(QueryEnhancedRequest.builder().queryConditional(queryConditional).build()).stream()
                    .flatMap(page -> page.items().stream())
                    .toList();

            if (users.isEmpty()) {
                long duration = System.currentTimeMillis() - startTime;
                log.debug("GSI查询无结果: {}={}, 耗时: {}ms", fieldName, queryValue, duration);
                return Optional.empty();
            }

            // 查找第一个未删除的用户
            Optional<UserDO> activeUser = users.stream()
                    .filter(user -> !Boolean.TRUE.equals(user.getDeleted()))
                    .findFirst();

            if (activeUser.isEmpty()) {
                long duration = System.currentTimeMillis() - startTime;
                log.debug("GSI查询无有效用户: {}={}, 耗时: {}ms", fieldName, queryValue, duration);
                return Optional.empty();
            }

            UserDO user = activeUser.get();
            long duration = System.currentTimeMillis() - startTime;
            log.info("GSI查询成功: {}={} -> userId={}, 耗时: {}ms", fieldName, queryValue, user.getUserId(), duration);

            // 性能监控：使用常量配置的阈值
            if (duration > DynamoDBConstants.GSI_QUERY_SLOW_THRESHOLD_MS) {
                log.warn("GSI查询耗时过长: {}={}, 耗时: {}ms (可能原因: 网络延迟或AWS服务负载)", fieldName, queryValue, duration);
            } else if (duration > 500) {
                log.info("GSI查询响应较慢: {}={}, 耗时: {}ms", fieldName, queryValue, duration);
            }

            return Optional.of(user);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("GSI查询失败: {}={}, 耗时: {}ms, error={}", fieldName, queryValue, duration, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 获取用户表
     */
    private DynamoDbTable<UserDO> getUserTable() {
        String tableName = tablePrefix + "User";
        return enhancedClient.table(tableName, TableSchema.fromBean(UserDO.class));
    }
}