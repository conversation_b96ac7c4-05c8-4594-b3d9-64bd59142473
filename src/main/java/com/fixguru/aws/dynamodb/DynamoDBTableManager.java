package com.fixguru.aws.dynamodb;

import com.fixguru.constants.SystemConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.AttributeDefinition;
import software.amazon.awssdk.services.dynamodb.model.BillingMode;
import software.amazon.awssdk.services.dynamodb.model.CreateTableRequest;
import software.amazon.awssdk.services.dynamodb.model.CreateTableResponse;
import software.amazon.awssdk.services.dynamodb.model.DeleteTableRequest;
import software.amazon.awssdk.services.dynamodb.model.DescribeTableRequest;
import software.amazon.awssdk.services.dynamodb.model.DescribeTableResponse;
import software.amazon.awssdk.services.dynamodb.model.KeySchemaElement;
import software.amazon.awssdk.services.dynamodb.model.KeyType;
import software.amazon.awssdk.services.dynamodb.model.ListTablesResponse;
import software.amazon.awssdk.services.dynamodb.model.ResourceInUseException;
import software.amazon.awssdk.services.dynamodb.model.ResourceNotFoundException;
import software.amazon.awssdk.services.dynamodb.model.ScalarAttributeType;
import software.amazon.awssdk.services.dynamodb.model.TableStatus;

import java.util.List;

/**
 * DynamoDB表管理器
 * 负责创建和管理DynamoDB表
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DynamoDBTableManager implements CommandLineRunner {

    private final DynamoDbClient dynamoDbClient;
    private final String tablePrefix;

    @Override
    public void run(String... args) {
        log.info("开始初始化DynamoDB表...");
        
        try {
            createUserTable();
            log.info("DynamoDB表初始化完成");
        } catch (Exception e) {
            log.error("DynamoDB表初始化失败", e);
        }
    }

    /**
     * 创建用户表
     */
    private void createUserTable() {
        String tableName = tablePrefix + SystemConstants.TABLE_USER;

        if (tableExists(tableName)) {
            log.info("表已存在，跳过创建: {}", tableName);
            return;
        }

        log.info("创建用户表: {}", tableName);

        // 使用GSITableManager创建带有GSI索引的表
        try {
            GSITableManager gsiTableManager = new GSITableManager(dynamoDbClient, tablePrefix);
            gsiTableManager.createUserTableWithGSI();
            log.info("用户表创建成功: {}", tableName);

            // 等待表创建完成
            waitForTableActive(tableName);
        } catch (ResourceInUseException e) {
            log.info("表已存在: {}", tableName);
        } catch (Exception e) {
            log.error("创建用户表失败: {}", tableName, e);
            throw new RuntimeException("创建用户表失败", e);
        }
    }

    /**
     * 检查表是否存在
     */
    private boolean tableExists(String tableName) {
        try {
            DescribeTableRequest request = DescribeTableRequest.builder()
                    .tableName(tableName)
                    .build();
            
            DescribeTableResponse response = dynamoDbClient.describeTable(request);
            return response.table().tableStatus() == TableStatus.ACTIVE;
        } catch (ResourceNotFoundException e) {
            return false;
        } catch (Exception e) {
            log.warn("检查表是否存在时发生错误: {}", tableName, e);
            return false;
        }
    }

    /**
     * 等待表变为活跃状态
     */
    private void waitForTableActive(String tableName) {
        log.info("等待表变为活跃状态: {}", tableName);
        
        int maxAttempts = 30;
        int attempt = 0;
        
        while (attempt < maxAttempts) {
            try {
                DescribeTableRequest request = DescribeTableRequest.builder()
                        .tableName(tableName)
                        .build();
                
                DescribeTableResponse response = dynamoDbClient.describeTable(request);
                TableStatus status = response.table().tableStatus();
                
                if (status == TableStatus.ACTIVE) {
                    log.info("表已变为活跃状态: {}", tableName);
                    return;
                }
                
                log.debug("表状态: {}, 等待中...", status);
                Thread.sleep(2000);
                attempt++;
            } catch (Exception e) {
                log.warn("检查表状态时发生错误: {}", tableName, e);
                attempt++;
            }
        }
        
        log.warn("等待表变为活跃状态超时: {}", tableName);
    }

    /**
     * 列出所有表
     */
    public List<String> listTables() {
        try {
            ListTablesResponse response = dynamoDbClient.listTables();
            return response.tableNames();
        } catch (Exception e) {
            log.error("列出表失败", e);
            throw new RuntimeException("列出表失败", e);
        }
    }

    /**
     * 删除表
     */
    public void deleteTable(String tableName) {
        try {
            DeleteTableRequest request = DeleteTableRequest.builder()
                    .tableName(tableName)
                    .build();
            
            dynamoDbClient.deleteTable(request);
            log.info("表删除成功: {}", tableName);
        } catch (ResourceNotFoundException e) {
            log.warn("表不存在: {}", tableName);
        } catch (Exception e) {
            log.error("删除表失败: {}", tableName, e);
            throw new RuntimeException("删除表失败", e);
        }
    }
}
