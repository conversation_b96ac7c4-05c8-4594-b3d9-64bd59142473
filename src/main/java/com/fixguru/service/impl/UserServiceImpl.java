package com.fixguru.service.impl;

import com.fixguru.aws.dynamodb.DynamoDBService;
import com.fixguru.aws.dynamodb.GSIQueryService;
import com.fixguru.config.EmailConfig;
import com.fixguru.constants.SystemConstants;
import com.fixguru.domain.UserDO;
import com.fixguru.dto.AuthTokenDTO;
import com.fixguru.dto.UserInfoDTO;
import com.fixguru.dto.request.ForgotPasswordRequest;
import com.fixguru.dto.request.ResetPasswordRequest;
import com.fixguru.dto.request.UserCreateRequest;
import com.fixguru.dto.request.UserLoginRequest;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.enums.UserStatusEnum;
import com.fixguru.exception.BizException;
import com.fixguru.service.EmailService;
import com.fixguru.service.TokenService;
import com.fixguru.service.UserService;
import com.fixguru.utils.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final DynamoDBService dynamoDBService;
    private final GSIQueryService gsiQueryService;
    private final TokenService tokenService;
    private final EmailService emailService;
    private final JwtUtils jwtUtils;
    private final EmailConfig emailConfig;

    /**
     * 获取用户表
     */
    private DynamoDbTable<UserDO> getUserTable() {
        return dynamoDBService.getTable(UserDO.class, SystemConstants.TABLE_USER);
    }

    @Override
    public UserInfoDTO createUser(UserCreateRequest request) {

        // 验证同意条款
        if (request.getAgreeToTerms() == null || !request.getAgreeToTerms()) {
            throw new BizException(BizCodeEnum.TERMS_NOT_AGREED);
        }

        // 检查邮箱是否已存在
        if (existsByEmail(request.getEmail())) {
            throw new BizException(BizCodeEnum.EMAIL_ALREADY_EXISTS);
        }

        // 创建用户实体
        UserDO userDO = new UserDO();
        userDO.setUserId(IdUtils.generateUserId());

        // 验证用户名是否符合规范
        if (!UserNameUtils.isValidUserName(request.getUserName())) {
            log.warn("用户名不符合规范: userName={}", request.getUserName());
            throw new BizException(BizCodeEnum.USERNAME_INVALID_CHARS, "User name does not meet requirements");
        }

        // 处理已验证的用户名
        String userName = UserNameUtils.processValidatedUserName(request.getUserName());

        // 确保用户名唯一
        String finalUserName = ensureUniqueUserName(userName);
        userDO.setUserName(finalUserName);
        userDO.setEmail(request.getEmail());
        userDO.setPasswordHash(PasswordUtils.hashPassword(request.getPassword()));
        userDO.setAvatarUrl(SystemConstants.DEFAULT_AVATAR_URL);
        userDO.setStatus(UserStatusEnum.PENDING);
        Instant now = Instant.now();
        userDO.setCreatedTime(now);
        userDO.setUpdatedTime(now);
        userDO.setCreatedBy(SystemConstants.SYSTEM_USER);
        userDO.setUpdatedBy(SystemConstants.SYSTEM_USER);
        userDO.setVersion(0L);
        // 保存用户
        DynamoDbTable<UserDO> table = getUserTable();
        dynamoDBService.save(table, userDO);

        // 异步发送激活邮件
        sendActivationEmailAsync(userDO);

        // 异步记录注册事件（用于数据分析）
//        recordUserRegistrationEventAsync(userDO, request);

        log.info("用户注册成功: userName={}, email={}, userId={}, status={}", finalUserName, request.getEmail(), userDO.getUserId(), userDO.getStatus());

        return convertToUserInfoDTO(userDO);
    }

    @Override
    public AuthTokenDTO login(UserLoginRequest request) {

        // 根据邮箱查找用户
        UserDO userDO = findUserByEmail(request.getEmail());
        if (userDO == null) {
            log.info("登录失败，用户不存在: email={}", request.getEmail());
            throw new BizException(BizCodeEnum.USER_NOT_FOUND);
        }

        // 检查用户是否可以登录
        if (!canUserLogin(userDO.getUserId())) {
            BizCodeEnum errorCode = getLoginErrorCode(userDO.getStatus());
            log.info("登录失败，用户状态不允许登录: email={}, userId={}, status={}", request.getEmail(), userDO.getUserId(), userDO.getStatus());
            throw new BizException(errorCode);
        }

        // 验证密码
        if (!PasswordUtils.verifyPassword(request.getPassword(), userDO.getPasswordHash())) {
            // 记录登录失败
            handleLoginFailure(userDO);
            log.info("登录失败，密码错误: email={}, userId={}, failureCount={}",
                    request.getEmail(), userDO.getUserId(), userDO.getLoginFailureCount());
            throw new BizException(BizCodeEnum.INVALID_CREDENTIALS);
        }

        // 登录成功，更新最后登录时间，清除失败计数
        Instant now = Instant.now();
        userDO.setLastLoginTime(now);
        userDO.setLoginFailureCount(0);
        userDO.setLastLoginFailureTime(null);
        userDO.setUpdatedTime(now);
        userDO.setUpdatedBy(userDO.getUserId());

        DynamoDbTable<UserDO> table = getUserTable();
        dynamoDBService.save(table, userDO);

        // 生成JWT令牌
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserId(userDO.getUserId());
        userInfoDTO.setEmail(userDO.getEmail());
        userInfoDTO.setUserName(userDO.getUserName());

        AuthTokenDTO authTokenDTO = tokenService.generateAuthToken(userInfoDTO);
        authTokenDTO.setUserInfo(convertToUserInfoDTO(userDO));

        log.info("用户登录成功: email={}, userId={}, status={}", request.getEmail(), userDO.getUserId(), userDO.getStatus());
        return authTokenDTO;
    }

    @Override
    public UserInfoDTO getUserById(String userId) {
        log.debug("开始获取用户信息: userId={}", userId);

        // 验证用户访问权限
        validateUserAccess(userId);

        // 查找用户并验证存在性
        UserDO user = findUserByIdOrThrow(userId);

        log.info("用户查询成功: userId={}, userName={}", userId, user.getUserName());
        return convertToUserInfoDTO(user);
    }

    @Override
    public UserInfoDTO getUserByUserName(String userName) {
        UserDO userDO = findUserByUserName(userName);
        if (userDO == null) {
            log.info("用户不存在: userName={}", userName);
            throw new BizException(BizCodeEnum.USER_NOT_FOUND);
        }
        log.info("用户查询成功: userName={}, userId={}", userName, userDO.getUserId());
        return convertToUserInfoDTO(userDO);
    }

    @Override
    public UserInfoDTO getUserByEmail(String email) {
        UserDO userDO = findUserByEmail(email);
        if (userDO == null) {
            log.info("用户不存在: email={}", email);
            throw new BizException(BizCodeEnum.USER_NOT_FOUND);
        }
        log.info("用户查询成功: email={}, userId={}", email, userDO.getUserId());
        return convertToUserInfoDTO(userDO);
    }

    @Override
    public boolean existsByUserName(String userName) {
        boolean exists = findUserByUserName(userName) != null;
        log.info("用户名检查: userName={}, exists={}", userName, exists);
        return exists;
    }

    @Override
    public boolean existsByEmail(String email) {
        boolean exists = findUserByEmail(email) != null;
        log.info("邮箱检查: email={}, exists={}", email, exists);
        return exists;
    }


    @Override
    public void forgotPassword(ForgotPasswordRequest request) {
        // 检查用户是否存在
        UserDO userDO = findUserByEmail(request.getEmail());
        if (userDO == null) {
            // 为了安全考虑，即使用户不存在也返回成功，不暴露用户是否存在的信息
            log.info("忘记密码请求，用户不存在: email={}", request.getEmail());
            return;
        }

        // 检查用户状态
        if (userDO.getStatus() == UserStatusEnum.DISABLED) {
            log.info("忘记密码请求，用户已被禁用: email={}, userId={}", request.getEmail(), userDO.getUserId());
            return;
        }

        try {
            // 检查发送频率限制
            if (!emailService.canSendResetEmail(request.getEmail())) {
                log.warn("忘记密码请求频率限制: email={}, userId={}", request.getEmail(), userDO.getUserId());
                throw new BizException(BizCodeEnum.EMAIL_SEND_RATE_LIMITED);
            }

            // 检查每日发送次数限制
            if (!emailService.canSendDailyLimit(request.getEmail())) {
                log.warn("忘记密码请求每日限制: email={}, userId={}", request.getEmail(), userDO.getUserId());
                throw new BizException(BizCodeEnum.EMAIL_DAILY_LIMIT_EXCEEDED);
            }

            // 生成重置密码令牌
            UserInfoDTO userInfo = convertToUserInfoDTO(userDO);
            String resetToken = jwtUtils.generateResetPasswordToken(userInfo, emailConfig.getResetTokenExpirationMinutes());

            // 异步发送重置密码邮件 - 不阻塞用户请求
            emailService.sendPasswordResetEmail(request.getEmail(), resetToken, userDO.getUserName());

            // AWS环境智能等待，平衡用户体验和邮件发送成功率
            EnvironmentUtils.waitForAsyncTaskIfNeeded("重置密码邮件");

            // 记录发送时间和每日发送次数（提前记录，避免重复请求）
            emailService.recordEmailSentTime(request.getEmail());
            emailService.recordDailySendCount(request.getEmail());

            log.info("忘记密码请求处理成功（异步发送邮件）: email={}, userId={}", request.getEmail(), userDO.getUserId());

        } catch (BizException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("忘记密码处理异常: email={}, userId={}, error={}",
                    request.getEmail(), userDO.getUserId(), e.getMessage(), e);
            throw new BizException(BizCodeEnum.EMAIL_SEND_FAILED);
        }
    }

    @Override
    public void resetPassword(String resetToken, ResetPasswordRequest request) {
        // 验证密码确认
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw new BizException(BizCodeEnum.PASSWORD_MISMATCH);
        }

        // 验证重置令牌
        if (!validateResetToken(resetToken)) {
            throw new BizException(BizCodeEnum.RESET_TOKEN_INVALID);
        }

        try {
            // 从令牌中获取用户信息
            UserInfoDTO userInfo = jwtUtils.getUserInfoFromToken(resetToken);
            if (userInfo == null || userInfo.getUserId() == null) {
                log.warn("重置密码失败: 无法从令牌中获取用户信息");
                throw new BizException(BizCodeEnum.RESET_TOKEN_INVALID);
            }

            // 查找用户
            UserDO userDO = findUserByIdOrThrow(userInfo.getUserId());

            // 检查用户状态
            if (userDO.getStatus() == UserStatusEnum.DISABLED) {
                log.warn("重置密码失败: 用户已被禁用, userId={}", userInfo.getUserId());
                throw new BizException(BizCodeEnum.USER_DISABLED);
            }

            // 检查令牌是否已被使用（通过比较令牌生成时间和用户最后更新时间）
            Instant resetTime = jwtUtils.getResetTimeFromToken(resetToken);
            if (resetTime != null && userDO.getUpdatedTime() != null && !resetTime.isAfter(userDO.getUpdatedTime())) {
                log.warn("重置令牌已被使用: userId={}, tokenTime={}, lastUpdate={}",
                        userInfo.getUserId(), resetTime, userDO.getUpdatedTime());
                throw new BizException(BizCodeEnum.RESET_TOKEN_INVALID);
            }

            // 更新密码
            String newPasswordHash = PasswordUtils.hashPassword(request.getNewPassword());
            Instant now = Instant.now();
            userDO.setPasswordHash(newPasswordHash);
            userDO.setUpdatedTime(now);
            userDO.setUpdatedBy(SystemConstants.SYSTEM_USER);

            // 保存到数据库
            DynamoDbTable<UserDO> table = getUserTable();
            dynamoDBService.save(table, userDO);

            log.info("密码重置成功: userId={}, email={}", userInfo.getUserId(), userInfo.getEmail());

        } catch (BizException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("重置密码异常: resetToken={}, error={}",
                    resetToken != null ? "***" : "null", e.getMessage(), e);
            // 为了安全，统一返回令牌无效错误，但保留详细日志用于调试
            throw new BizException(BizCodeEnum.RESET_TOKEN_INVALID);
        }
    }

    @Override
    public boolean validateResetToken(String resetToken) {
        if (resetToken == null || resetToken.trim().isEmpty()) {
            return false;
        }

        try {
            // 使用JWT工具类验证重置令牌
            return jwtUtils.validateResetPasswordToken(resetToken);
        } catch (Exception e) {
            log.warn("重置令牌验证异常: error={}", e.getMessage());
            return false;
        }
    }

    /**
     * 确保显示名称唯一
     * 如果显示名称已存在，直接拼接时间戳后四位
     */
    private String ensureUniqueUserName(String baseUserName) {
        // 首先检查原始显示名称
        if (!existsByUserName(baseUserName)) {
            return baseUserName;
        }

        // 如果显示名称已存在，直接拼接时间戳后四位
        long timestamp = System.currentTimeMillis() % 10000;
        String uniqueUserName = baseUserName + timestamp;

        log.debug("显示名称冲突解决: '{}' -> '{}'", baseUserName, uniqueUserName);
        return uniqueUserName;
    }

    /**
     * 处理登录失败
     */
    private void handleLoginFailure(UserDO userDO) {
        // 更新登录失败次数和时间
        int failureCount = userDO.getLoginFailureCount() != null ? userDO.getLoginFailureCount() : 0;
        failureCount++;

        Instant now = Instant.now();
        userDO.setLoginFailureCount(failureCount);
        userDO.setLastLoginFailureTime(now);
        userDO.setUpdatedTime(now);
        userDO.setUpdatedBy(SystemConstants.SYSTEM_USER);

        // 保存失败记录
        DynamoDbTable<UserDO> table = getUserTable();
        dynamoDBService.save(table, userDO);

        log.warn("用户登录失败: userId={}, failureCount={}", userDO.getUserId(), failureCount);

        // 自动锁定逻辑：如果登录失败次数达到限制，自动锁定账户
        if (failureCount >= SystemConstants.SECURITY_MAX_LOGIN_ATTEMPTS) {
            lockUser(userDO.getUserId(), "登录失败次数过多，自动锁定账户15分钟");
            log.warn("用户账户已自动锁定: userId={}, failureCount={}", userDO.getUserId(), failureCount);
        }
    }

    /**
     * 根据用户状态获取对应的业务错误码
     */
    private BizCodeEnum getLoginErrorCode(UserStatusEnum status) {
        switch (status) {
            case LOCKED:
                return BizCodeEnum.USER_LOCKED;
            case DISABLED:
                return BizCodeEnum.USER_DISABLED;
            default:
                return BizCodeEnum.INVALID_CREDENTIALS;
        }
    }

    /**
     * 根据用户名查找用户
     * 使用GSI（全局二级索引）进行查询
     */
    private UserDO findUserByUserName(String userName) {
        return gsiQueryService.findUserByUserName(userName).orElse(null);
    }

    /**
     * 根据邮箱查找用户
     * 使用GSI（全局二级索引）进行查询
     */
    private UserDO findUserByEmail(String email) {
        return gsiQueryService.findUserByEmail(email).orElse(null);
    }

    /**
     * 根据用户ID查找用户
     */
    private UserDO findUserById(String userId) {
        DynamoDbTable<UserDO> table = getUserTable();
        return dynamoDBService.findByKey(table, userId).orElse(null);
    }

    /**
     * 根据用户ID查找用户，如果不存在则抛出异常
     */
    private UserDO findUserByIdOrThrow(String userId) {
        UserDO user = findUserById(userId);
        if (user == null || (user.getDeleted() != null && user.getDeleted())) {
            log.warn("用户不存在: userId={}", userId);
            throw new BizException(BizCodeEnum.USER_NOT_FOUND);
        }
        return user;
    }

    /**
     * 验证用户访问权限（用户资源隔离）
     */
    private void validateUserAccess(String targetUserId) {
        String currentUserId = UserContextUtils.getCurrentUserId();

        // 检查用户上下文是否正确设置
        if (currentUserId == null) {
            log.error("用户上下文为空，可能是认证拦截器配置问题 - 请求用户ID: {}", targetUserId);
            throw new BizException(BizCodeEnum.UNAUTHORIZED, "Authentication required. Please login first.");
        }

        // 检查用户是否有权限访问目标用户资源
        if (!UserContextUtils.hasAccessToUser(targetUserId)) {
            log.warn("用户资源访问被拒绝: 当前用户{}尝试访问用户{}的资源", currentUserId, targetUserId);
            throw new BizException(BizCodeEnum.FORBIDDEN, "Access denied. You can only access your own resources.");
        }
    }

    /**
     * 验证用户状态转换是否允许
     */
    private void validateStatusTransition(UserDO user, UserStatusEnum targetStatus, String operation) {
        if (!user.getStatus().canTransitionTo(targetStatus)) {
            log.warn("{}失败，状态不允许转换: userId={}, currentStatus={} -> {}",
                    operation, user.getUserId(), user.getStatus(), targetStatus);
            throw new BizException(BizCodeEnum.INVALID_REQUEST,
                    String.format("Cannot transition from %s to %s", user.getStatus(), targetStatus));
        }
    }

    /**
     * 转换为用户信息DTO
     */
    private UserInfoDTO convertToUserInfoDTO(UserDO userDO) {
        try {
            UserInfoDTO userInfoDTO = JsonUtils.convert(userDO, UserInfoDTO.class);
            return userInfoDTO;
        } catch (Exception e) {
            log.error("用户信息转换失败: userId={}, error={}", userDO.getUserId(), e.getMessage(), e);
            // 如果转换失败，手动创建响应对象
            return createUserInfoDTOManually(userDO);
        }
    }

    /**
     * 手动创建用户信息DTO（备用方法）
     */
    private UserInfoDTO createUserInfoDTOManually(UserDO userDO) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserId(userDO.getUserId());
        userInfoDTO.setUserName(userDO.getUserName());
        userInfoDTO.setEmail(userDO.getEmail());
        userInfoDTO.setAvatarUrl(userDO.getAvatarUrl());
        userInfoDTO.setStatus(userDO.getStatus());
        userInfoDTO.setLastLoginTime(userDO.getLastLoginTime());
        userInfoDTO.setCreatedTime(userDO.getCreatedTime());
        userInfoDTO.setUpdatedTime(userDO.getUpdatedTime());
        return userInfoDTO;
    }


    @Override
    public boolean lockUser(String userId, String reason) {
        try {
            UserDO userDO = findUserByIdOrThrow(userId);
            validateStatusTransition(userDO, UserStatusEnum.LOCKED, "锁定用户");

            // 设置锁定到期时间（15分钟后自动解锁）
            Instant lockExpiresAt = Instant.now().plusSeconds(SystemConstants.SECURITY_ACCOUNT_LOCK_MINUTES * 60);
            userDO.setLockExpiresAt(lockExpiresAt);

            return changeUserStatus(userDO, UserStatusEnum.LOCKED, reason);
        } catch (BizException e) {
            log.warn("锁定用户失败: userId={}, reason={}, error={}", userId, reason, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean unlockUser(String userId, String reason) {
        try {
            UserDO userDO = findUserByIdOrThrow(userId);

            if (userDO.getStatus() != UserStatusEnum.LOCKED) {
                log.warn("解锁用户失败，用户未被锁定: userId={}, currentStatus={}", userId, userDO.getStatus());
                return false;
            }

            return changeUserStatus(userDO, UserStatusEnum.ACTIVE, reason);
        } catch (BizException e) {
            log.warn("解锁用户失败: userId={}, reason={}, error={}", userId, reason, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean disableUser(String userId, String adminId, String reason) {
        try {
            UserDO userDO = findUserByIdOrThrow(userId);
            validateStatusTransition(userDO, UserStatusEnum.DISABLED, "禁用用户");
            return changeUserStatus(userDO, UserStatusEnum.DISABLED, reason);
        } catch (BizException e) {
            log.warn("禁用用户失败: userId={}, adminId={}, reason={}, error={}", userId, adminId, reason, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean enableUser(String userId, String adminId, String reason) {
        try {
            UserDO userDO = findUserByIdOrThrow(userId);

            if (userDO.getStatus() != UserStatusEnum.DISABLED) {
                log.warn("启用用户失败，用户未被禁用: userId={}, currentStatus={}", userId, userDO.getStatus());
                return false;
            }

            return changeUserStatus(userDO, UserStatusEnum.ACTIVE, reason);
        } catch (BizException e) {
            log.warn("启用用户失败: userId={}, adminId={}, reason={}, error={}", userId, adminId, reason, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean canUserLogin(String userId) {
        UserDO userDO = findUserById(userId);
        if (userDO == null) {
            return false;
        }

        // 如果用户被锁定，检查是否已过15分钟锁定期
        if (userDO.getStatus() == UserStatusEnum.LOCKED) {
            return checkAndAutoUnlockUser(userDO);
        }

        return userDO.getStatus().isCanLogin();
    }

    /**
     * 检查并自动解锁用户（15分钟锁定期）
     */
    private boolean checkAndAutoUnlockUser(UserDO userDO) {
        if (userDO.getLockExpiresAt() == null) {
            // 如果没有锁定到期时间记录，说明是手动锁定，不自动解锁
            log.debug("用户被手动锁定，不自动解锁: userId={}", userDO.getUserId());
            return false;
        }

        Instant now = Instant.now();
        Instant lockExpiresAt = userDO.getLockExpiresAt();

        // 如果锁定已到期，自动解锁
        if (now.isAfter(lockExpiresAt)) {
            // 自动解锁用户
            userDO.setStatus(UserStatusEnum.ACTIVE);
            userDO.setLockExpiresAt(null);
            userDO.setLoginFailureCount(0);
            userDO.setLastLoginFailureTime(null);
            userDO.setUpdatedTime(now);
            userDO.setUpdatedBy(SystemConstants.SYSTEM_USER);

            // 保存用户信息
            DynamoDbTable<UserDO> userTable = getUserTable();
            dynamoDBService.save(userTable, userDO);

            // 计算实际锁定时长（更简洁的写法）
            Instant lockStartTime = lockExpiresAt.minusSeconds(SystemConstants.SECURITY_ACCOUNT_LOCK_MINUTES * 60L);
            Duration actualLockDuration = Duration.between(lockStartTime, now);
            log.info("用户自动解锁成功: userId={}, 锁定时长={}分钟", userDO.getUserId(), actualLockDuration.toMinutes());
            return true;
        }

        // 还在锁定期内 - 计算剩余时间（更简洁的写法）
        Duration remainingDuration = Duration.between(now, lockExpiresAt);
        long remainingMinutes = remainingDuration.toMinutes();
        long remainingSeconds = remainingDuration.getSeconds() % 60;
        log.debug("用户仍在锁定期内: userId={}, 剩余锁定时间={}分钟{}秒", userDO.getUserId(), remainingMinutes, remainingSeconds);
        return false;
    }

    @Override
    public Map<String, Object> getUserStatus(String userId) {
        log.debug("开始获取用户状态: userId={}", userId);

        // 验证用户访问权限
        validateUserAccess(userId);

        // 获取当前用户信息
        UserInfoDTO currentUser = UserContextUtils.getCurrentUser();
        if (currentUser == null) {
            throw new BizException(BizCodeEnum.UNAUTHORIZED, "User context not found");
        }

        // 获取用户登录状态
        boolean canLogin = canUserLogin(userId);

        Map<String, Object> statusInfo = new HashMap<>();
        statusInfo.put("status", currentUser.getStatus());
        statusInfo.put("canLogin", canLogin);

        log.debug("返回用户状态: userId={}, status={}", userId, currentUser.getStatus());
        return statusInfo;
    }

    /**
     * 变更用户状态
     */
    private boolean changeUserStatus(UserDO userDO, UserStatusEnum newStatus, String reason) {
        UserStatusEnum oldStatus = userDO.getStatus();
        if (oldStatus == newStatus) {
            log.info("用户状态未变更: userId={}, currentStatus={}", userDO.getUserId(), oldStatus);
            return false;
        }
        userDO.setStatus(newStatus);
        userDO.setUpdatedTime(Instant.now());
        userDO.setUpdatedBy(SystemConstants.SYSTEM_USER);

        // 保存用户信息
        DynamoDbTable<UserDO> userTable = getUserTable();
        dynamoDBService.save(userTable, userDO);

        log.info("用户状态变更成功: userId={}, {} -> {}, reason={}", userDO.getUserId(), oldStatus, newStatus, reason);
        return true;
    }

    /**
     * 异步记录用户注册事件（用于数据分析）
     *
     * @param userDO  用户实体
     * @param request 注册请求
     */
    @Async
    protected void recordUserRegistrationEventAsync(UserDO userDO, UserCreateRequest request) {
        try {
            // 记录注册事件的详细信息
            log.info("记录用户注册事件: userId={}, email={}, userName={}, registrationTime={}, subscribeUpdates={}",
                    userDO.getUserId(),
                    userDO.getEmail(),
                    request.getUserName(),
                    userDO.getCreatedTime(),
                    request.getSubscribeUpdates());

            // TODO: 这里可以扩展更多数据分析相关的逻辑，例如：
            // 1. 发送注册事件到数据分析系统（如AWS Kinesis、CloudWatch等）
            // 2. 记录用户注册来源、设备信息等
            // 3. 触发欢迎邮件发送
            // 4. 更新用户统计数据
            // 5. 记录到专门的事件表中

            // 模拟异步处理时间
            Thread.sleep(100);

            log.info("用户注册事件记录完成: userId={}", userDO.getUserId());

        } catch (Exception e) {
            // 异步处理失败不应该影响主流程，只记录错误日志
            log.error("记录用户注册事件失败: userId={}, error={}", userDO.getUserId(), e.getMessage(), e);
        }
    }

    @Override
    public Instant getUserLastLogoutTime(String userId) {
        UserDO userDO = findUserById(userId);
        return userDO != null ? userDO.getLastLogoutTime() : null;
    }

    @Override
    public void updateUserLastLogoutTime(String userId, Instant logoutTime) {
        UserDO userDO = findUserById(userId);
        if (userDO != null) {
            userDO.setLastLogoutTime(logoutTime);
            userDO.setUpdatedTime(Instant.now());

            DynamoDbTable<UserDO> table = getUserTable();
            dynamoDBService.save(table, userDO);

            log.info("更新用户最后登出时间: userId={}, logoutTime={}", userId, logoutTime);
        } else {
            log.warn("更新登出时间失败，用户不存在: userId={}", userId);
        }
    }

    @Override
    public UserInfoDTO activateUser(String activateToken) {
        // 验证激活令牌
        if (!jwtUtils.validateActivateToken(activateToken)) {
            log.warn("激活失败，令牌无效: token={}", activateToken);
            throw new BizException(BizCodeEnum.ACTIVATE_TOKEN_INVALID);
        }

        // 从令牌中获取用户ID
        String userId = jwtUtils.getUserIdFromActivateToken(activateToken);
        if (userId == null) {
            log.warn("激活失败，无法从令牌中获取用户ID: token={}", activateToken);
            throw new BizException(BizCodeEnum.ACTIVATE_TOKEN_INVALID);
        }

        // 查找用户
        UserDO userDO = findUserById(userId);
        if (userDO == null) {
            log.warn("激活失败，用户不存在: userId={}", userId);
            throw new BizException(BizCodeEnum.USER_NOT_FOUND);
        }

        // 检查用户当前状态
        if (userDO.getStatus() == UserStatusEnum.ACTIVE) {
            log.info("用户已经激活: userId={}, email={}", userId, userDO.getEmail());
            throw new BizException(BizCodeEnum.USER_ALREADY_ACTIVATED);
        }

        if (userDO.getStatus() != UserStatusEnum.PENDING) {
            log.warn("激活失败，用户状态不正确: userId={}, status={}", userId, userDO.getStatus());
            throw new BizException(BizCodeEnum.ACTIVATE_FAILED);
        }

        // 激活用户
        userDO.setStatus(UserStatusEnum.ACTIVE);
        userDO.setUpdatedTime(Instant.now());
        userDO.setUpdatedBy(userId);

        DynamoDbTable<UserDO> table = getUserTable();
        dynamoDBService.save(table, userDO);

        log.info("用户激活成功: userId={}, email={}", userId, userDO.getEmail());

        return convertToUserInfoDTO(userDO);
    }

    /**
     * 异步发送激活邮件
     * 生成激活令牌并发送邮件
     *
     * @param userDO 用户实体
     */
    private void sendActivationEmailAsync(UserDO userDO) {
        try {
            log.info("准备发送激活邮件: userId={}, email={}", userDO.getUserId(), userDO.getEmail());

            // 生成激活令牌
            UserInfoDTO userInfo = convertToUserInfoDTO(userDO);
            String activateToken = jwtUtils.generateActivateToken(userInfo, emailConfig.getActivateTokenExpirationHours());

            // 异步发送激活邮件
            emailService.sendActivateEmail(
                    userDO.getEmail(),
                    activateToken,
                    userDO.getUserName()
            );

            log.info("激活邮件发送请求已提交: userId={}, email={}", userDO.getUserId(), userDO.getEmail());

            // AWS环境智能等待，平衡用户体验和邮件发送成功率
            EnvironmentUtils.waitForAsyncTaskIfNeeded("激活邮件");

        } catch (Exception e) {
            // 邮件发送失败不应该影响注册流程，只记录错误日志
            log.error("发送激活邮件失败: userId={}, email={}, error={}",
                    userDO.getUserId(), userDO.getEmail(), e.getMessage(), e);
        }
    }

}
