package com.fixguru.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 用户名处理工具类
 * 严格的用户名生成和验证逻辑
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public final class UserNameUtils {

    private UserNameUtils() {
        // 工具类，禁止实例化
    }

    // 系统保留词
    private static final Set<String> RESERVED_WORDS = new HashSet<>(Arrays.asList(
        "root", "admin", "administrator", "system", "null", "undefined", "user", "guest",
        "test", "demo", "api", "www", "mail", "email", "support", "help", "info",
        "service", "manager", "operator", "moderator", "staff", "team", "official",
        "bot", "robot", "auto", "noreply", "postmaster", "webmaster"
    ));

    // 敏感词列表
    private static final Set<String> SENSITIVE_WORDS = new HashSet<>(Arrays.asList(
        "fuck", "shit", "damn", "bitch", "asshole", "bastard", "crap", "hell",
        "sex", "porn", "xxx", "adult", "casino", "gambling", "bet", "loan",
        "spam", "scam", "fake", "fraud", "cheat", "hack", "crack", "pirate"
    ));

    // 验证模式
    private static final Pattern ALLOWED_CHARS = Pattern.compile("^[\\p{L}\\p{N}_]+$");
    private static final Pattern REPEATED_CHARS = Pattern.compile("(.)\\1{2,}");
    private static final Pattern EMAIL_PATTERN = Pattern.compile(".*@.*\\.[a-zA-Z]{2,}.*");
    private static final Pattern PHONE_PATTERN = Pattern.compile(".*\\d{3,}[-\\s]?\\d{3,}[-\\s]?\\d{3,}.*");
    private static final Pattern URL_PATTERN = Pattern.compile(".*(http|www|\\.[a-zA-Z]{2,}).*");

    /**
     * 处理已验证的用户名
     * 对通过isValidUsername验证的用户名进行必要的规范化处理
     *
     * @param userName 已验证的用户名
     * @return 处理后的用户名
     */
    public static String processValidatedUserName(String userName) {
        if (userName == null || userName.trim().isEmpty()) {
            throw new IllegalArgumentException("userName cannot be null or empty");
        }

        // 基本清理和规范化
        String processed = userName.trim()
                // 去除所有空格（防御性处理）
                .replaceAll("\\s+", "")
                // 多个连续下划线合并为一个
                .replaceAll("_+", "_")
                // 去除开头和结尾的下划线
                .replaceAll("^_+|_+$", "");

        // 确保处理后仍然符合最小长度要求
        if (processed.length() < 3) {
            log.warn("用户名处理后长度不足: original='{}', processed='{}'", userName, processed);
            throw new IllegalArgumentException("User name too short after processing");
        }

        log.debug("用户名处理: '{}' -> '{}'", userName, processed);
        return processed;
    }

    /**
     * 验证用户名是否符合规范
     *
     * @param userName 用户名
     * @return 是否有效
     */
    public static boolean isValidUserName(String userName) {
        if (userName == null || userName.trim().isEmpty()) {
            return false;
        }

        // 长度检查
        if (userName.length() < 3 || userName.length() > 20) {
            return false;
        }

        // 字符检查：仅允许字母、数字、下划线
        if (!ALLOWED_CHARS.matcher(userName).matches()) {
            return false;
        }

        // 禁止数字开头
        if (Character.isDigit(userName.charAt(0))) {
            return false;
        }

        // 禁止下划线结尾
        if (userName.endsWith("_")) {
            return false;
        }

        // 重复字符检测
        if (REPEATED_CHARS.matcher(userName).find()) {
            return false;
        }

        // 系统保留词检查
        if (RESERVED_WORDS.contains(userName.toLowerCase())) {
            return false;
        }

        // 敏感词检查
        if (containsSensitiveWords(userName.toLowerCase())) {
            return false;
        }

        // 联系方式检查
        return !containsContactInfo(userName);
    }

    /**
     * 检查是否包含敏感词
     */
    private static boolean containsSensitiveWords(String userName) {
        return SENSITIVE_WORDS.stream().anyMatch(userName::contains);
    }

    /**
     * 检查是否包含联系方式信息
     */
    private static boolean containsContactInfo(String userName) {
        return EMAIL_PATTERN.matcher(userName).matches() ||
               PHONE_PATTERN.matcher(userName).matches() ||
               URL_PATTERN.matcher(userName).matches();
    }

}
