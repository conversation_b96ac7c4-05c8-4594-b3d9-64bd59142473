# FixGuru 初始架构设计参考文档

> ⚠️ **注意**: 这是项目初期的架构设计文档，实际实现可能与此文档有所不同。当前项目采用了更简化的架构，主要使用DynamoDB作为数据存储，未使用Redis缓存。

## 📋 项目概述

### 项目背景
基于 www.ifixit.com 网站内容，开发iOS端APP（后续扩展Android），为海外用户提供设备维修指南服务。已完成Python爬虫数据采集，现需构建后端API服务。

### 技术目标
- **本地开发优先**：保证开发体验流畅性
- **AWS Lambda部署**：最终无服务器架构托管
- **移动端适配**：支持iOS/Android统一API
- **海外用户服务**：AWS全球基础设施支持

## 🏗️ 整体架构设计

### 架构理念
```
本地开发 → AWS适配 → Lambda部署
   ↓           ↓          ↓
快速开发    平滑过渡    无服务器
```

### 技术栈选择

#### 核心框架
- **JDK**: 17 (LTS版本，Lambda原生支持)
- **SpringBoot**: 3.2.5 (稳定版，修复了早期3.x版本问题)
- **Maven**: 3.9.6+ (依赖管理)

#### 数据层技术 (AWS原生NoSQL架构)
- **NoSQL数据库**: AWS DynamoDB (无服务器，自动扩展)
- **DynamoDB SDK**: AWS SDK for Java 2.x (官方推荐)
- **DynamoDB映射**: DynamoDBMapper / DynamoDBEnhancedClient
- **本地开发**: 直接连接AWS DynamoDB (简化开发环境)

#### 开发工具
- **API文档**: Knife4j 4.4.0 (完全支持SpringBoot3 + Jakarta EE)
- **日志框架**: @Slf4j + Logback (SpringBoot3默认)
- **代码简化**: Lombok 1.18.30 (完全支持JDK17)
- **参数验证**: Spring Boot Starter Validation (SpringBoot3默认)

#### AWS服务集成
- **计算**: AWS Lambda (Java17运行时)
- **API网关**: API Gateway
- **数据库**: DynamoDB (NoSQL)
- **存储**: S3
- **认证**: JWT Token
- **监控**: CloudWatch

### ⚠️ 版本兼容性确认

#### JDK17 + SpringBoot3 兼容性验证
```yaml
核心兼容性检查:
  JDK17 + SpringBoot3.2.5: ✅ 完全兼容
  MyBatis-Plus 3.5.5: ✅ 官方支持SpringBoot3，使用Jakarta EE
  Knife4j 4.4.0: ✅ 专门为SpringBoot3设计，支持Jakarta EE
  Lombok 1.18.30: ✅ 完全支持JDK17，无兼容性问题
  MySQL Connector/J 8.2.0: ✅ SpringBoot3默认驱动，替代旧版connector-java
  Redis + Lettuce 6.3.x: ✅ SpringBoot3默认Redis客户端，完全兼容
  AWS SDK 2.25.11: ✅ 最新稳定版，完全支持JDK17

关键变更说明:
  1. SpringBoot3使用Jakarta EE替代Java EE
  2. MySQL驱动从mysql-connector-java改为mysql-connector-j
  3. Redis客户端默认使用Lettuce替代Jedis(性能更好，支持异步)
  4. Knife4j必须使用jakarta版本(knife4j-openapi3-jakarta-spring-boot-starter)
  5. 所有依赖都经过SpringBoot3兼容性测试

潜在问题预防:
  - 避免使用javax.*包，改用jakarta.*
  - Redis配置使用Lettuce连接池而非Jedis
  - 确保所有第三方库都支持SpringBoot3
  - 使用SpringBoot3推荐的依赖版本
```

## 📁 项目结构设计

### 功能模块分层架构
```
iFixit-Backend/
├── src/main/java/com/ifixit/
│   ├── controller/                 # REST API控制器层
│   │   ├── HealthController.java   # 健康检查
│   │   ├── AuthController.java     # 用户认证
│   │   └── ... # 其他业务Controller (根据实际需求扩展)
│   ├── service/                   # 业务逻辑接口层
│   │   ├── CacheService.java       # 缓存服务接口
│   │   ├── AuthService.java        # 认证服务接口
│   │   ├── ... # 其他业务Service接口
│   │   └── impl/                   # 业务逻辑实现层
│   │       ├── CacheServiceImpl.java
│   │       ├── AuthServiceImpl.java
│   │       └── ... # 其他业务ServiceImpl实现
│   ├── mapper/                    # 数据访问层
│   │   ├── BaseMapper.java         # 通用数据访问基类
│   │   ├── UserMapper.java         # 用户数据访问
│   │   └── ... # 其他业务Mapper接口
│   ├── do/                        # 数据实体层
│   │   ├── BaseDO.java             # 通用实体基类
│   │   ├── UserDO.java             # 用户实体
│   │   └── ... # 其他业务DO实体
│   ├── dto/                       # 业务数据传输对象
│   │   ├── request/               # 请求DTO
│   │   │   ├── UserLoginRequest.java
│   │   │   ├── UserRegisterRequest.java
│   │   │   └── ... # 其他业务Request
│   │   ├── AuthTokenDTO.java      # 认证令牌DTO
│   │   ├── UserInfoDTO.java       # 用户信息DTO
│   │   └── ... # 其他业务DTO
│   ├── config/                    # 配置类
│   │   ├── DynamoDBConfig.java
│   │   ├── RedisConfig.java
│   │   ├── CacheConfig.java
│   │   ├── WebConfig.java
│   │   └── AwsConfig.java
│   ├── common/                    # 通用基础组件
│   │   ├── BaseRequest.java        # 通用请求基类
│   │   ├── BasePageRequest.java    # 分页请求基类
│   │   ├── ApiResponse.java        # 统一响应格式
│   │   └── PageResponse.java       # 分页响应格式
│   ├── constants/                 # 常量定义
│   │   ├── SystemConstants.java
│   │   └── ... # 其他业务常量
│   ├── exception/                 # 异常处理
│   │   ├── GlobalExceptionHandler.java  # 全局异常处理器
│   │   ├── BizException.java            # 业务异常
│   │   └── SystemException.java         # 系统异常
│   ├── utils/                     # 工具类
│   │   ├── JsonUtils.java
│   │   ├── EncryptUtils.java
│   │   ├── ValidationUtils.java
│   │   └── ... # 其他工具类
│   ├── enums/                     # 枚举定义
│   │   ├── UserStatusEnum.java
│   │   ├── BizCodeEnum.java
│   │   └── ... # 其他业务枚举
│   ├── aws/                       # AWS服务适配层
│   │   ├── dynamodb/              # DynamoDB服务
│   │   │   ├── DynamoDBService.java
│   │   │   └── DynamoDBTableManager.java
│   │   ├── s3/                    # S3文件存储
│   │   │   └── S3FileStorageService.java
│   │   ├── cognito/               # 用户认证
│   │   │   └── CognitoAuthService.java
│   │   ├── sns/                   # 推送通知服务
│   │   │   └── NotificationService.java
│   │   ├── lambda/                # Lambda适配器
│   │   │   └── BaseLambdaHandler.java
│   │   └── common/                # AWS通用配置
│   │       └── AwsClientConfig.java
│   └── Application.java           # SpringBoot启动类
├── src/main/resources/
│   ├── application.yml            # 通用配置
│   ├── application-local.yml      # 本地开发配置
│   ├── application-aws.yml        # AWS环境配置
│   ├── application-test.yml       # 测试环境配置
│   ├── dynamodb/                  # DynamoDB表定义
│   │   ├── table-definitions/     # 表结构定义文件
│   │   └── local-setup/           # 本地DynamoDB初始化脚本
│   └── static/                    # 静态资源
├── src/test/java/                 # 测试代码
│   ├── unit/                      # 单元测试
│   ├── integration/               # 集成测试
│   └── BaseIntegrationTest.java   # 测试基类
├── docker/                        # Docker配置
│   ├── docker-compose.yml         # 本地开发环境
│   ├── dynamodb-local/            # DynamoDB Local配置
│   └── redis/                     # Redis配置
├── aws/                           # AWS部署配置
│   ├── cloudformation/            # CloudFormation模板
│   ├── lambda/                    # Lambda部署配置
│   └── scripts/                   # 部署脚本
├── docs/                          # 项目文档
│   ├── api/                       # API文档
│   └── deployment/                # 部署文档
├── pom.xml                        # Maven配置
└── README.md                      # 项目说明
```

## 🎯 核心功能模块设计

### 功能模块架构概览

基于实际业务需求，系统采用AWS原生服务的简洁功能架构：

```
iFixit iOS APP 功能架构 (AWS原生)
├── 基础服务层
│   ├── 用户认证服务 (AWS Cognito)
│   ├── 文件存储服务 (AWS S3)
│   ├── 缓存服务 (ElastiCache Redis)
│   └── 通知推送服务 (AWS SNS)
├── 核心业务层
│   ├── 用户认证模块 (User Authentication)
│   └── 其他业务模块 (根据实际需求扩展)
├── 数据访问层
│   ├── NoSQL数据访问 (DynamoDB)
│   └── 其他数据访问 (根据实际需求扩展)
└── AWS服务集成层
    ├── DynamoDB (主数据库)
    ├── Lambda (计算服务)
    ├── API Gateway (API管理)
    └── CloudWatch (监控日志)
```

### 核心功能模块说明

系统采用AWS原生NoSQL架构的模块化设计，支持灵活扩展各种业务功能。每个功能模块都遵循统一的架构规范：

- **Controller层**: 处理HTTP请求，参数验证，调用Service层
- **Service层**: 业务逻辑处理，调用Mapper层进行数据操作
- **Mapper层**: DynamoDB数据访问，NoSQL查询，数据映射
- **DO层**: 数据实体，对应DynamoDB表项结构，使用注解映射

#### DynamoDB设计原则
- **单表设计**: 尽可能使用单表存储相关数据，减少跨表查询
- **访问模式优先**: 根据查询需求设计主键和GSI
- **最终一致性**: 理解并合理使用DynamoDB的一致性模型
- **成本优化**: 合理使用按需计费和预置容量模式

各功能模块可根据实际业务需求进行设计和实现，保持架构的一致性和可维护性。

### 2. 用户管理模块 (User Management Module)

#### 注册登录功能设计
**2.1 用户注册流程**
- **注册方式**: 邮箱注册、手机号注册、社交账号登录(Google, Apple)
- **验证机制**: 邮箱验证码、短信验证码、图形验证码
- **数据收集**: 基础信息、用户偏好设置
- **隐私保护**: GDPR合规、数据加密存储

**2.2 用户登录认证**
- **认证方式**: JWT Token + Refresh Token机制
- **多端登录**: 支持多设备同时登录，会话管理
- **安全策略**: 登录失败锁定、异地登录提醒
- **第三方集成**: AWS Cognito用户池管理

#### 核心数据模型设计 (DynamoDB表设计)

**DynamoDB表结构设计：**

```json
{
  "TableName": "Users",
  "BillingMode": "PAY_PER_REQUEST",
  "KeySchema": [
    {
      "AttributeName": "id",
      "KeyType": "HASH"
    }
  ],
  "AttributeDefinitions": [
    {
      "AttributeName": "id",
      "AttributeType": "S"
    },
    {
      "AttributeName": "email",
      "AttributeType": "S"
    },
    {
      "AttributeName": "userName",
      "AttributeType": "S"
    }
  ],
  "GlobalSecondaryIndexes": [
    {
      "IndexName": "email-index",
      "KeySchema": [
        {
          "AttributeName": "email",
          "KeyType": "HASH"
        }
      ],
      "Projection": {
        "ProjectionType": "ALL"
      }
    },
    {
      "IndexName": "userName-index",
      "KeySchema": [
        {
          "AttributeName": "userName",
          "KeyType": "HASH"
        }
      ],
      "Projection": {
        "ProjectionType": "ALL"
      }
    }
  ]
}
```

**用户数据项结构 (对应UserDO)：**
```json
{
  "id": "user_12345",
  "userName": "john_doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "passwordHash": "hashed_password",
  "avatarUrl": "https://s3.amazonaws.com/avatars/user_12345.jpg",
  "emailVerified": true,
  "phoneVerified": false,
  "status": "ACTIVE",
  "lastLoginTime": "2024-01-15T10:30:00Z",
  "createdTime": "2024-01-01T00:00:00Z",
  "updatedTime": "2024-01-15T10:30:00Z"
}
```

**其他业务表 (根据实际需求设计)：**
```json
// 示例：其他业务表结构
// { "TableName": "BusinessTable", ... }
```

#### DynamoDB配置示例

**Spring Boot配置 (application.yml)：**
```yaml
# 本地开发环境
spring:
  profiles:
    active: local

aws:
  dynamodb:
    endpoint: http://localhost:8000  # DynamoDB Local
    region: us-west-1
    access-key: dummy
    secret-key: dummy

---
# AWS生产环境
spring:
  profiles: aws

aws:
  dynamodb:
    region: us-west-1
    # 使用IAM角色，无需配置access-key
```

**DynamoDB配置类：**
```java
@Configuration
@EnableDynamoDBRepositories
public class DynamoDBConfig {

    @Value("${aws.dynamodb.endpoint:}")
    private String dynamoDBEndpoint;

    @Value("${aws.dynamodb.region}")
    private String awsRegion;

    @Bean
    @Primary
    public DynamoDBMapper dynamoDBMapper() {
        return new DynamoDBMapper(amazonDynamoDB());
    }

    @Bean
    public AmazonDynamoDB amazonDynamoDB() {
        AmazonDynamoDBClientBuilder builder = AmazonDynamoDBClientBuilder.standard()
            .withRegion(awsRegion);

        // 本地开发环境配置
        if (StringUtils.hasText(dynamoDBEndpoint)) {
            builder.withEndpointConfiguration(
                new AwsClientBuilder.EndpointConfiguration(dynamoDBEndpoint, awsRegion)
            ).withCredentials(new AWSStaticCredentialsProvider(
                new BasicAWSCredentials("dummy", "dummy")
            ));
        }

        return builder.build();
    }
}
```

**UserDO实体类示例：**
```java
@DynamoDBTable(tableName = "Users")
public class UserDO extends BaseDO {

    @DynamoDBHashKey
    @DynamoDBAttribute(attributeName = "id")
    private String id;

    @DynamoDBAttribute(attributeName = "userName")
    private String userName;

    @DynamoDBAttribute(attributeName = "email")
    private String email;

    @DynamoDBAttribute(attributeName = "phone")
    private String phone;

    @DynamoDBAttribute(attributeName = "passwordHash")
    private String passwordHash;

    @DynamoDBAttribute(attributeName = "avatarUrl")
    private String avatarUrl;

    @DynamoDBAttribute(attributeName = "emailVerified")
    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.BOOL)
    private Boolean emailVerified;

    @DynamoDBAttribute(attributeName = "phoneVerified")
    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.BOOL)
    private Boolean phoneVerified;

    @DynamoDBTypeConvertedEnum
    @DynamoDBAttribute(attributeName = "status")
    private UserStatus status;

    @DynamoDBAttribute(attributeName = "lastLoginTime")
    private Date lastLoginTime;

    @DynamoDBAttribute(attributeName = "createdTime")
    private Date createdTime;

    @DynamoDBAttribute(attributeName = "updatedTime")
    private Date updatedTime;

    // 构造函数
    public UserDO() {
        this.id = UUID.randomUUID().toString();
        this.createdTime = new Date();
        this.updatedTime = new Date();
        this.status = UserStatus.ACTIVE;
        this.emailVerified = false;
        this.phoneVerified = false;
    }

    // getters and setters...
}
```

**UserMapper DynamoDB实现示例：**
```java
@Repository
public class UserMapper {

    @Autowired
    private DynamoDBMapper dynamoDBMapper;

    public UserDO findById(String id) {
        return dynamoDBMapper.load(UserDO.class, id);
    }

    public UserDO findByEmail(String email) {
        Map<String, AttributeValue> eav = new HashMap<>();
        eav.put(":email", new AttributeValue().withS(email));

        DynamoDBQueryExpression<UserDO> queryExpression =
            new DynamoDBQueryExpression<UserDO>()
                .withIndexName("email-index")
                .withConsistentRead(false)
                .withKeyConditionExpression("email = :email")
                .withExpressionAttributeValues(eav);

        List<UserDO> results = dynamoDBMapper.query(UserDO.class, queryExpression);
        return results.isEmpty() ? null : results.get(0);
    }

    public UserDO findByUsername(String userName) {
        Map<String, AttributeValue> eav = new HashMap<>();
        eav.put(":userName", new AttributeValue().withS(userName));

        DynamoDBQueryExpression<UserDO> queryExpression =
            new DynamoDBQueryExpression<UserDO>()
                .withIndexName("userName-index")
                .withConsistentRead(false)
                .withKeyConditionExpression("userName = :userName")
                .withExpressionAttributeValues(eav);

        List<UserDO> results = dynamoDBMapper.query(UserDO.class, queryExpression);
        return results.isEmpty() ? null : results.get(0);
    }

    public void save(UserDO user) {
        user.setUpdatedTime(new Date());
        dynamoDBMapper.save(user);
    }

    public void delete(UserDO user) {
        dynamoDBMapper.delete(user);
    }

    public void updateLastLoginTime(String userId) {
        UserDO user = findById(userId);
        if (user != null) {
            user.setLastLoginTime(new Date());
            save(user);
        }
    }
}
```

## 🗄️ 数据库设计策略

### 数据模型设计原则

#### 基于爬取数据的设计思路
由于您是基于Python爬虫获取的iFixit数据进行自主开发，数据模型需要根据实际爬取的数据结构来设计。建议采用以下策略：

1. **数据分析优先**：先分析爬取到的数据结构和字段
2. **灵活建表**：根据实际数据字段动态设计表结构
3. **预留扩展**：为后续功能扩展预留字段空间
4. **索引优化**：基于查询场景设计合理索引

#### 通用表设计规范
- **基础字段**: id、status、sort_order、remark等通用字段
- **审计字段**: created_by、updated_by、created_time、updated_time
- **通用索引**: 状态、时间等常用查询字段的索引

#### 数据迁移和初始化策略
- **crawl_data_temp**: 爬取数据导入临时表（原始JSON数据存储）
- **data_transform_log**: 数据清洗和转换日志表（处理过程记录）
- **设计原则**: 支持数据追踪、错误恢复、增量处理

### 4. 支付集成模块 (Payment Integration Module)

#### 支付功能设计
**4.1 支付场景**
- **订阅付费**: 高级会员订阅、专业版功能解锁
- **内容付费**: 高级维修指南、专家咨询服务
- **工具购买**: 推荐维修工具、配件购买链接
- **打赏功能**: 对优质内容创作者的打赏

**4.2 支付技术实现**
- **支付网关**: Stripe、PayPal、Apple Pay、Google Pay
- **支付安全**: PCI DSS合规、支付信息加密
- **订单管理**: 订单状态跟踪、支付回调处理
- **退款处理**: 自动退款、部分退款、争议处理

#### 支付和订阅数据模型
```sql
-- 订单表 (对应OrderDO)
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(32) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    order_type ENUM('SUBSCRIPTION', 'CONTENT', 'PREMIUM_FEATURE') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status ENUM('PENDING', 'PAID', 'CANCELLED', 'REFUNDED') DEFAULT 'PENDING',
    payment_method VARCHAR(50),
    payment_gateway VARCHAR(50),
    gateway_order_id VARCHAR(100),
    paid_time TIMESTAMP NULL,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 订阅表 (对应SubscriptionDO)
CREATE TABLE subscriptions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    plan_type ENUM('PREMIUM', 'PRO') NOT NULL,
    status ENUM('ACTIVE', 'CANCELLED', 'EXPIRED') DEFAULT 'ACTIVE',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    auto_renew BOOLEAN DEFAULT TRUE,
    order_id BIGINT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (order_id) REFERENCES orders(id)
);
```

### 5. 历史记录模块 (History Module)

#### 历史记录功能设计
**5.1 行为记录类型**
- **浏览历史**: 查看的维修指南、设备信息
- **搜索历史**: 搜索关键词、搜索结果点击
- **维修记录**: 完成的维修项目、维修进度
- **收藏记录**: 收藏的指南、设备、工具

**5.2 成就系统设计**
- **成就类型**: 维修达人、学习之星、社区贡献者、连续签到
- **成就等级**: 青铜、白银、黄金、钻石等级体系
- **奖励机制**: 积分奖励、徽章展示、特权解锁
- **社交分享**: 成就分享到社交媒体

#### 用户行为和成就数据模型
```sql
-- 历史记录表 (对应HistoryDO)
CREATE TABLE histories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    history_type ENUM('BROWSE', 'SEARCH', 'FAVORITE', 'REPAIR') NOT NULL,
    target_type ENUM('GUIDE', 'DEVICE', 'PHOTO') NOT NULL,
    target_id BIGINT NOT NULL,
    title VARCHAR(200),
    metadata JSON,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_history (user_id, history_type, created_time),
    INDEX idx_target (target_type, target_id)
);

-- 用户成就表 (对应AchievementDO)
CREATE TABLE achievements (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    achievement_type VARCHAR(50) NOT NULL,
    achievement_level ENUM('BRONZE', 'SILVER', 'GOLD', 'DIAMOND') DEFAULT 'BRONZE',
    progress INT DEFAULT 0,
    target_progress INT NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_time TIMESTAMP NULL,
    reward_points INT DEFAULT 0,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE KEY uk_user_achievement (user_id, achievement_type)
);
```

### 6. 订阅管理模块 (Subscription Management Module)

#### 订阅功能设计
**6.1 订阅计划**
- **免费版**: 基础维修指南、有限搜索次数
- **高级版**: 无限搜索、高清图片、专家指南
- **专业版**: AI识别、优先支持、离线下载

**6.2 订阅管理功能**
- **计划升级**: 免费升级付费、付费计划间切换
- **自动续费**: 订阅到期自动续费、续费失败处理
- **使用统计**: 功能使用次数统计、配额管理
- **权限控制**: 基于订阅状态的功能权限控制

### 存储策略设计

#### 本地开发环境
- **DynamoDB Local**: 本地DynamoDB实例，完全模拟生产环境
- **Redis**: 缓存热点数据、用户会话、临时数据处理
- **本地文件系统**: 图片和视频文件存储，模拟S3目录结构

#### AWS生产环境
- **DynamoDB**: 核心业务数据存储，支持高并发和自动扩展
- **ElastiCache Redis**: 分布式缓存、会话共享、实时数据
- **S3**: 图片、视频、文档等媒体文件存储，支持CDN加速
- **CloudWatch**: 日志存储和监控数据

#### DynamoDB数据处理流程设计
```yaml
DynamoDB数据处理流程:
  1. 数据写入: 应用 → DynamoDB表 (自动分区)
  2. 数据查询: 点查询(主键) / GSI查询(二级索引)
  3. 数据更新: 条件更新、原子计数器
  4. 数据备份: 自动备份、时间点恢复
  5. 数据同步: DynamoDB Streams → Lambda触发器

NoSQL数据访问模式:
  - 主键访问: 根据用户ID直接查询 (1-2ms延迟)
  - GSI查询: 根据email/username查询 (5-10ms延迟)
  - 批量操作: BatchGetItem/BatchWriteItem
  - 条件更新: 乐观锁、条件表达式

用户认证数据流:
  用户注册 → 数据验证 → DynamoDB写入 → GSI更新
  用户登录 → GSI查询 → 密码验证 → Token生成 → Redis缓存
  Token刷新 → 主键查询 → 权限验证 → 新Token生成
```

## 🔧 核心框架和通用组件设计

### 通用框架设计

#### 基础框架组件
- **通用基础类和接口**: BaseEntity、BaseRequest等
- **统一响应格式**: ApiResponse、PageResponse
- **全局异常处理**: GlobalExceptionHandler、BusinessException
- **通用工具类**: JsonUtils、DateUtils等
- **配置管理**: 多环境配置、参数外部化
- **安全框架**: 认证授权、数据加密
- **缓存框架**: Redis缓存管理
- **文件处理框架**: 本地/S3存储适配

### 核心通用组件设计

#### 1. 常量管理框架
- **SystemConstants**: 系统常量统一管理（状态、客户端类型、缓存键、文件类型、API版本等）
- **BizCodeEnum**: 业务响应码统一管理（成功码、错误码和对应的英文消息）
- **设计原则**: 集中管理，避免硬编码，便于维护

#### 2. 统一响应格式框架
- **ApiResponse<T>**: 统一API响应格式（code、message、data、timestamp）
- **PageResponse<T>**: 分页响应格式（records、total、current、size等）
- **设计原则**: 标准化响应，便于前端处理

### Maven依赖配置

#### 核心依赖版本
```yaml
SpringBoot: 3.2.5
JDK: 17
Maven: 3.9.6+

主要依赖:
  - spring-boot-starter-web
  - spring-boot-starter-validation
  - spring-boot-starter-data-redis
  - mybatis-plus-boot-starter: 3.5.5
  - mysql-connector-j: 8.2.0
  - knife4j-openapi3-jakarta-spring-boot-starter: 4.4.0
  - lombok: 1.18.30
  - aws-sdk-bom: 2.25.11
  - testcontainers (测试)
```

#### 3. 全局异常处理框架
- **GlobalExceptionHandler**: 全局异常处理器（参数验证异常、业务异常、系统异常）
- **BusinessException**: 自定义业务异常类
- **设计原则**: 统一异常处理，标准化错误响应

#### 4. 通用工具类框架
- **JsonUtils**: JSON序列化/反序列化工具类
- **CacheService**: 通用Redis缓存服务（set、get、delete等操作）
- **设计原则**: 封装常用操作，统一异常处理

#### 5. 文件处理框架
- **FileStorageService**: 文件存储服务接口（支持本地/S3切换）
- **LocalFileStorageService**: 本地文件存储实现
- **S3FileStorageService**: AWS S3文件存储实现（后续开发）
- **设计原则**: 接口抽象，支持多种存储方式

#### 6. 数据处理框架
- **DataProcessService**: 爬取数据处理服务（数据验证、转换、保存）
- **设计原则**: 通用处理框架，具体逻辑根据实际数据结构实现

### 环境配置管理

#### 配置文件结构
- **application.yml**: 通用配置（服务端口、数据源、Redis、MyBatis-Plus、日志、Knife4j等）
- **application-local.yml**: 本地开发配置
- **application-aws.yml**: AWS环境配置
- **application-test.yml**: 测试环境配置

#### 配置要点
- **本地开发**: 使用localhost数据库和Redis，LocalStack模拟AWS服务
- **AWS环境**: 使用环境变量配置RDS、ElastiCache、S3等服务
- **文件存储**: 支持本地存储和S3存储的动态切换
- **日志级别**: 开发环境debug，生产环境info

## 🌐 API设计规范

### RESTful API设计原则
```yaml
API设计规范:
  路径设计:
    - 使用名词而非动词
    - 使用复数形式
    - 层级关系清晰
    - 版本控制统一

  HTTP方法:
    - GET: 查询操作
    - POST: 创建操作
    - PUT: 更新操作
    - DELETE: 删除操作

  状态码使用:
    - 200: 成功
    - 201: 创建成功
    - 400: 请求参数错误
    - 401: 未认证
    - 403: 无权限
    - 404: 资源不存在
    - 500: 服务器错误

iOS APP功能模块API路径设计:
  # 基础服务API
  /api/v1/health              # 健康检查
  /api/v1/cache/clear         # 缓存清理

  # 用户认证API
  /api/v1/auth/register       # 用户注册
  /api/v1/auth/login          # 用户登录
  /api/v1/auth/logout         # 用户登出
  /api/v1/auth/refresh        # Token刷新

  # 其他业务功能API (根据实际需求设计)
  # /api/v1/xxx/...           # 具体业务API
```

### 请求响应规范
- **BaseRequest**: 通用请求基类（requestId、clientType、clientVersion、language等）
- **BasePageRequest**: 分页请求基类（current、size、orderBy、keyword等）
- **参数验证**: 使用@Valid注解进行参数校验
- **设计原则**: 统一请求格式，便于链路追踪和客户端适配

## 🔒 安全设计

### 认证授权策略
- **JWT令牌认证**: 使用标准JWT实现用户认证
- **令牌类型**: 使用"FixGuru"替代传统"Bearer"，体现品牌特色
- **移动端优化**:
  - Access Token: 24小时有效期，适合移动端长时间使用
  - Refresh Token: 30天有效期，减少重新登录频率
  - 自动刷新机制: Access Token还有2小时过期时自动刷新
- **安全机制**:
  - JWT包含用户基本信息，减少数据库查询
  - 支持令牌撤销和黑名单机制
  - 设备指纹验证增强安全性
- **API安全**: 统一的认证拦截器
- **权限控制**: 基于角色的访问控制(RBAC)

### JWT认证架构详细设计

#### 令牌结构设计
```yaml
# Access Token (24小时有效期)
Header:
  alg: HS256
  typ: JWT

Payload:
  userId: "user_1234567890_5678"
  email: "<EMAIL>"
  userName: "john_doe"
  tokenType: "access"
  iss: "FixGuru-Service"
  iat: 1640995200
  exp: 1641081600

# Refresh Token (30天有效期)
Payload:
  userId: "user_1234567890_5678"
  email: "<EMAIL>"
  userName: "john_doe"
  tokenType: "refresh"
  iss: "FixGuru-Service"
  iat: 1640995200
  exp: 1643587200
```

#### 令牌使用流程
1. **用户登录**: 验证用户名密码后生成Access Token和Refresh Token
2. **API访问**: 请求头使用`Authorization: FixGuru <access_token>`
3. **自动刷新**: Access Token还有2小时过期时，客户端自动使用Refresh Token刷新
4. **令牌撤销**: 用户登出时撤销所有令牌

#### 移动端优化特性
- **长有效期**: 24小时Access Token，30天Refresh Token
- **静默刷新**: 用户无感知的令牌刷新机制
- **离线支持**: JWT包含用户基本信息，减少网络依赖
- **安全保障**: 设备指纹验证、异常登录检测

### 数据安全
- **敏感数据加密**: 用户密码、个人信息
- **JWT安全**: 使用HS256算法签名，密钥安全存储
- **令牌安全**: 支持令牌黑名单和撤销机制
- **SQL注入防护**: MyBatis-Plus参数化查询
- **XSS防护**: 输入参数验证和转义
- **HTTPS强制**: 生产环境强制HTTPS

## 📊 性能优化策略

### 缓存策略
- **Redis缓存管理器**: 统一缓存配置，支持TTL和序列化
- **缓存层级**: L1本地缓存 + L2Redis分布式缓存
- **缓存策略**: 热点数据缓存、查询结果缓存、会话缓存
- **失效策略**: 基于TTL的自动失效 + 手动清理

### 数据库优化
- **连接池配置**: HikariCP连接池优化
- **索引设计**: 基于查询模式的索引优化
- **分页查询**: 避免深度分页性能问题
- **读写分离**: 后期考虑主从分离

### API性能优化
- **响应压缩**: Gzip压缩
- **异步处理**: 耗时操作异步化
- **批量操作**: 减少数据库交互次数
- **CDN加速**: 静态资源CDN分发

## 🚀 Lambda适配策略

### Lambda函数设计
- **BaseLambdaHandler**: Lambda处理器基类，统一请求处理流程
- **函数粒度**: 按业务模块拆分Lambda函数（设备、指南、用户等）
- **请求处理**: 预处理 → 业务逻辑 → 响应构建
- **错误处理**: 统一异常处理和错误响应格式
- **CORS支持**: 跨域请求头配置

### 冷启动优化
- **预留并发**: 配置预留并发避免冷启动
- **内存优化**: 合理配置Lambda内存大小
- **依赖精简**: 减少不必要的依赖包
- **初始化优化**: 静态初始化减少启动时间

## 📈 监控和运维

### 日志管理
- **LoggingAspect**: AOP日志切面，记录API调用时间和结果
- **日志级别**: 开发环境DEBUG，生产环境INFO
- **日志格式**: 统一的时间戳、线程、级别、消息格式
- **日志收集**: 本地文件 + CloudWatch日志流

### 健康检查
- **HealthController**: 健康检查控制器，提供系统状态监控
- **基础检查**: /health 简单状态检查
- **详细检查**: /health/detail 包含数据库、Redis、系统信息
- **监控指标**: 连接状态、响应时间、系统资源使用情况

## 🧪 测试策略

### 测试金字塔
- **单元测试 (70%)**: Service层业务逻辑测试
- **集成测试 (20%)**: API接口和数据库集成测试
- **端到端测试 (10%)**: 完整业务流程测试

### 测试配置
- **BaseIntegrationTest**: 集成测试基类，使用TestContainers
- **测试容器**: MySQL 8.0 + Redis 7.0 容器化测试环境
- **动态配置**: 自动配置测试数据库和Redis连接
- **测试隔离**: 每个测试类独立的容器实例

## 📅 实施计划

### 阶段一：基础框架搭建 (第1-2周)
- [ ] 项目结构创建和Maven配置
- [ ] Docker Compose本地开发环境搭建(DynamoDB Local + Redis)
- [ ] 基础实体类和通用组件开发
- [ ] DynamoDB连接和配置
- [ ] Redis缓存配置和通用缓存服务
- [ ] 统一异常处理和响应格式
- [ ] 健康检查和监控接口
- [ ] API文档配置(Knife4j)

### 阶段二：DynamoDB数据框架开发 (第3-5周)
- [ ] DynamoDB表结构设计和创建脚本
- [ ] DynamoDB Local环境搭建和测试
- [ ] DynamoDBMapper集成和配置
- [ ] 开发通用NoSQL数据访问框架
- [ ] 实现文件存储服务(本地/S3适配)
- [ ] 建立NoSQL数据验证和完整性检查机制
- [ ] 开发基础DynamoDB CRUD操作模板
- [ ] 完善缓存服务和工具类

### 阶段三：核心功能模块开发 (第6-10周)

#### 第6-7周：用户认证模块
- [ ] 用户注册登录功能实现
- [ ] JWT Token认证机制
- [ ] 第三方登录集成(Google, Apple)
- [ ] 用户权限和角色管理

#### 第8-10周：其他业务模块开发
- [ ] 根据实际业务需求开发相应功能模块
- [ ] 遵循统一的架构规范和代码标准
- [ ] 完善API接口和数据模型设计
- [ ] 编写单元测试和集成测试

### 阶段四：用户行为和成就系统 (第11-12周)
- [ ] 用户行为记录和分析
- [ ] 浏览历史和收藏功能
- [ ] 成就系统设计和实现
- [ ] 积分奖励和等级体系
- [ ] 用户活跃度统计
- [ ] 个性化推荐算法

### 阶段五：AWS集成开发 (第13-15周)
- [ ] AWS SDK集成和配置
- [ ] S3文件存储服务集成
- [ ] Cognito用户认证集成
- [ ] DynamoDB生产环境配置和优化
- [ ] ElastiCache集成和缓存策略
- [ ] SNS推送通知服务集成
- [ ] 环境配置管理(本地DynamoDB Local/AWS DynamoDB切换)
- [ ] AWS服务监控和日志(CloudWatch)

### 阶段六：Lambda适配和优化 (第16-18周)
- [ ] Lambda Handler开发
- [ ] API Gateway集成配置
- [ ] 冷启动优化和性能调优
- [ ] CloudFormation部署模板
- [ ] 生产环境部署和测试
- [ ] 监控报警和运维配置
- [ ] 安全审计和合规检查
- [ ] 文档完善和项目交付

### 关键里程碑
- **第2周末**: 基础框架搭建完成，DynamoDB Local环境可用
- **第5周末**: DynamoDB数据框架完成，NoSQL数据访问层可用
- **第7周末**: 用户认证模块完成，支持注册登录
- **第10周末**: 其他业务模块根据实际需求完成开发
- **第15周末**: AWS DynamoDB集成完成，云环境可部署
- **第18周末**: Lambda部署成功，生产环境稳定运行，项目交付

### 开发注意事项
- 先搭建通用框架，再根据实际数据开发具体业务
- 避免过早设计具体实体类，保持架构灵活性
- 重点关注通用组件的复用性和扩展性
- 确保本地开发环境和AWS环境的一致性

## 💰 成本预估

### AWS服务成本 (月度预估)
- **Lambda**: $50-100 (基于调用次数)
- **RDS MySQL**: $100-150 (db.t3.micro实例)
- **ElastiCache Redis**: $80-120 (cache.t3.micro实例)
- **S3存储**: $20-50 (基于存储量和传输)
- **API Gateway**: $30-60 (基于API调用次数)
- **其他服务**: $50-100 (CloudWatch、Cognito等)
- **总计**: $330-580/月

### 成本优化建议
- 使用AWS免费层资源
- 合理配置资源规格
- 设置预算报警
- 定期清理无用资源

## 🛡️ 风险控制

### 技术风险
- **AWS服务不熟悉**: 渐进式学习和实践
- **Lambda冷启动**: 预留并发和优化策略
- **数据迁移风险**: 完善的备份和回滚机制

### 业务风险
- **海外用户体验**: CDN加速和多区域部署
- **数据安全合规**: GDPR等法规遵循
- **服务可用性**: 多可用区部署和容灾

### 运维风险
- **监控报警**: 全方位监控覆盖
- **故障恢复**: 自动化运维和快速恢复
- **成本控制**: 预算管理和成本优化

---

## 📝 总结

本架构设计基于您的技术背景和项目需求，采用**改进的传统分层架构**，既保持了SpringBoot开发的熟悉性，又为AWS Lambda部署做好了准备。

### 核心优势
1. **技术栈熟悉**: 基于SpringBoot生态，保持开发习惯
2. **功能模块完整**: 涵盖用户管理、设备识别、图像处理、搜索、支付、订阅等完整功能
3. **数据驱动**: 基于爬取数据的灵活设计，避免过度设计
4. **渐进式演进**: 从本地开发到Lambda部署的平滑过渡
5. **通用组件**: 完善的基础组件和工具类，提高开发效率
6. **AWS适配**: 专门的适配层设计，支持云原生服务
7. **移动端优化**: 针对iOS/Android的API设计和性能优化

### 关键特性
- 🏗️ **架构完整**: 8大核心功能模块，覆盖完整业务流程
- 📱 **移动端友好**: 三种拍照功能，支持设备识别和维修记录
- 🔍 **智能搜索**: 多维度搜索，个性化推荐，搜索历史
- 💳 **支付集成**: 多种支付方式，订阅管理，安全合规
- 🏆 **用户激励**: 成就系统，积分奖励，用户等级体系
- 🤖 **AI增强**: AWS Rekognition设备识别，图像处理优化
- 🔧 **配置灵活**: 多环境配置支持，本地/云端无缝切换
- 📊 **数据处理**: 完整的爬取数据处理和转换流程
- 🌐 **API标准**: RESTful API和统一响应格式
- ⚡ **性能优化**: 缓存策略和数据库优化
- 🛡️ **安全可靠**: 认证授权和数据安全保护

### 特别考虑的设计点

#### 1. 数据模型灵活性
- 不预设具体实体类，根据爬取数据动态设计
- 提供通用基础实体类，包含审计字段
- 支持树形结构数据的通用处理

#### 2. 爬取数据处理
- 专门的数据处理服务，支持多种数据类型
- 完整的数据清洗和转换流程
- 数据处理日志和错误追踪

#### 3. 通用组件设计
- 统一的常量管理，避免硬编码
- 通用缓存服务，支持多种缓存操作
- 文件存储抽象，支持本地和S3切换

#### 4. 开发效率考虑
- 完善的基础类和工具类
- 统一的异常处理和响应格式
- 自动化的数据处理流程

#### 5. 扩展性设计
- 模块化的项目结构
- 接口抽象和适配器模式
- 环境配置的外部化管理

## 📋 iOS APP功能模块详细规划表

| 功能模块 | 核心功能 | 对应Controller | 技术实现 | 优先级 | 开发周期 |
|---------|---------|---------------|---------|--------|----------|
| **用户认证模块** | 注册登录、JWT令牌管理、令牌刷新 | UserController | JWT认证、FixGuru令牌类型 | 🔴 高 | ✅ 已完成 |
| **其他业务模块** | 根据实际需求设计 | XxxController | 具体技术实现 | 待定 | 待定 |

### 功能模块依赖关系

```mermaid
graph LR
    A[用户管理模块] --> B[图像处理模块]
    A --> C[设备识别模块]
    A --> D[搜索引擎模块]
    A --> E[支付集成模块]
    A --> F[用户行为模块]
    A --> G[订阅管理模块]

    B --> C
    C --> H[维修指南模块]
    D --> H
    E --> G
    F --> D

    style A fill:#ff9999
    style B fill:#99ccff
    style C fill:#99ff99
    style D fill:#ffcc99
    style E fill:#ff99cc
    style F fill:#ccff99
    style G fill:#99ffcc
    style H fill:#ffff99
```

### 三种拍照功能详细设计

| 拍照类型 | 使用场景 | 技术特点 | 数据流程 | 业务价值 |
|---------|---------|---------|---------|---------|
| **设备识别拍照** | 用户不知道设备型号时拍照识别 | AI识别、设备匹配、置信度评分 | 拍照→预处理→AI识别→设备匹配→返回结果 | 提升用户体验，快速找到对应指南 |
| **维修记录拍照** | 维修过程中记录每个步骤 | 步骤关联、进度追踪、质量检测 | 拍照→压缩存储→关联步骤→生成记录 | 帮助用户记录维修过程，分享经验 |
| **问题反馈拍照** | 遇到问题时拍照求助 | 社区集成、问题分类、专家回复 | 拍照→问题描述→社区发布→获得帮助 | 构建用户社区，提供互助平台 |

### 🚨 异常处理设计说明

基于企业级应用的最佳实践，异常处理应该简洁而有效。推荐使用**两层异常体系**：

| 异常类型 | 异常类名 | 使用场景 | 示例 |
|---------|---------|---------|------|
| **业务异常** | BizException | 业务逻辑错误、用户操作错误 | 用户不存在、余额不足、权限不够 |
| **系统异常** | SystemException | 系统级错误、第三方服务异常 | 数据库连接失败、AWS服务异常、网络超时 |

#### 异常设计原则
- **BizException**: 可预期的业务异常，通常需要返回给用户友好的错误信息
- **SystemException**: 不可预期的系统异常，通常记录日志，返回通用错误信息
- **GlobalExceptionHandler**: 统一异常处理，根据异常类型返回不同的响应格式

#### 为什么不需要更多异常类？
1. **PaymentException** → 归类为BusinessException，通过错误码区分
2. **PhotoProcessException** → 归类为SystemException，属于技术异常
3. **过多异常类** → 增加维护成本，违反简单性原则

### 📝 代码命名规范总结

| 层级 | 命名规范 | 示例 | 说明 |
|------|---------|------|------|
| **Controller层** | XxxController | AuthController, UserController | 控制器类，处理HTTP请求 |
| **Service接口层** | XxxService | AuthService, UserService | 业务逻辑接口定义 |
| **Service实现层** | XxxServiceImpl | AuthServiceImpl, UserServiceImpl | 业务逻辑具体实现 |
| **Mapper层** | XxxMapper | UserMapper, PhotoMapper | 数据访问接口 |
| **DO实体层** | XxxDO | UserDO, PhotoDO | 数据库实体对象，基类为BaseDO |
| **Request层** | XxxRequest | UserLoginRequest, PhotoUploadRequest | 请求参数对象 |
| **DTO层** | XxxDTO | UserInfoDTO, AuthTokenDTO | 数据传输对象 |
| **Constants层** | XxxConstants | SystemConstants, PhotoConstants | 常量定义类 |
| **Exception层** | XxxException | BizException, SystemException | 异常类定义 |
| **Utils层** | XxxUtils | JsonUtils, PhotoUtils | 工具类定义 |
| **Enums层** | XxxType/XxxStatus | PhotoType, UserStatus | 枚举类定义 |

### 🏗️ 架构设计特点

✅ **扁平化结构** - Controller、Service、Mapper、DO层不再分包，便于管理
✅ **统一命名规范** - Request/DTO/DO后缀，保持代码一致性
✅ **接口实现分离** - Service层分离接口和实现，便于测试和扩展
✅ **iOS APP导向** - 功能模块设计完全贴合iOS APP的实际需求
✅ **三种拍照功能** - 统一的PhotoController处理不同类型的拍照需求
✅ **DO实体设计** - do包下的DO实体对应数据库表，清晰的数据映射关系
✅ **简化异常处理** - 两层异常体系，避免过度设计
✅ **通用组件提取** - BaseRequest、ApiResponse等移到common包统一管理

### 📊 架构调整对比表

| 调整项目 | 调整前 | 调整后 | 说明 |
|---------|--------|--------|------|
| **数据库选择** | MySQL关系型数据库 | DynamoDB NoSQL数据库 | 更适合AWS Lambda和简单查询 |
| **数据访问** | MyBatis-Plus ORM | DynamoDBMapper | 原生AWS SDK，性能更优 |
| **实体包名** | entity/ | do/ | 更符合DO命名规范 |
| **实体基类** | BaseEntity.java | BaseDO.java | 简化命名，统一DO后缀 |
| **实体注解** | JPA注解 | DynamoDB注解 | 适配NoSQL数据模型 |
| **DTO结构** | dto/扁平化 | dto/request/ + dto根目录DTO | request子包 + DTO直接放在dto包下 |
| **通用组件** | common/constants/<br/>common/exception/<br/>common/utils/<br/>common/enums/ | constants/<br/>exception/<br/>utils/<br/>enums/ | 扁平化，与common同层级 |
| **基础DTO** | dto/request/BaseRequest<br/>dto/response/ApiResponse | common/BaseRequest<br/>common/ApiResponse | 移到common包统一管理 |
| **异常设计** | BusinessException | BizException | 简化异常类命名 |
| **核心功能** | 包含用户管理、用户偏好等 | 只保留用户认证功能 | 进一步简化，专注最核心功能 |
| **数据模型** | 关系型表结构 | NoSQL文档结构 | 适配DynamoDB数据模型 |

### 🎯 最终架构特点

✅ **AWS原生架构** - 采用DynamoDB NoSQL数据库，完美适配AWS Lambda
✅ **无服务器设计** - DynamoDB自动扩展，零运维负担
✅ **成本优化** - 按需付费模式，小规模应用成本极低
✅ **性能卓越** - 毫秒级延迟，支持高并发访问
✅ **完全扁平化** - 所有层级都采用扁平化设计，便于管理
✅ **DO包设计** - 独立的do包，BaseDO统一基类，DynamoDB注解映射
✅ **通用组件提取** - BaseRequest、ApiResponse等移到common包
✅ **简化异常体系** - 两层异常设计，避免过度复杂化
✅ **极简核心功能** - 只保留用户认证的最核心功能
✅ **统一命名规范** - 严格的Request/DTO/DO后缀规范
✅ **接口实现分离** - Service接口和ServiceImpl实现分离
✅ **高度灵活** - 最小化设计，支持任意方向的业务扩展

这个基于DynamoDB的极简化架构设计专注于最核心的用户认证功能，采用AWS原生NoSQL解决方案，完全避免了过度设计，为您的iFixit iOS APP后端开发提供了最清晰、最简洁、最高性能、最具成本效益的架构基础。
