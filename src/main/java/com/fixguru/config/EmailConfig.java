package com.fixguru.config;

import com.fixguru.constants.EmailConstants;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.util.StringUtils;

import java.util.Properties;

/**
 * 邮件服务配置类
 * 从配置文件中读取邮件相关配置并配置JavaMailSender
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "app.email")
public class EmailConfig {

    /**
     * 发送方邮箱地址
     */
    private String fromEmail;

    /**
     * 发送方显示名称
     */
    private String fromName;

    /**
     * 是否启用邮件服务
     */
    private Boolean enabled;

    /**
     * 重置密码令牌过期时间（分钟）
     */
    private Integer resetTokenExpirationMinutes;

    /**
     * 重置密码邮件主题
     */
    private String resetPasswordSubject;

    /**
     * 前端重置密码页面URL
     */
    private String resetPasswordUrl;

    /**
     * 激活令牌过期时间（小时）
     */
    private Integer activateTokenExpirationHours;

    /**
     * 激活邮件主题
     */
    private String activateEmailSubject;

    /**
     * 前端激活页面URL
     */
    private String activateUrl;

    /**
     * 邮件发送频率限制（分钟）
     * 同一邮箱在此时间内只能发送一次重置邮件
     */
    private Integer sendRateLimitMinutes;

    /**
     * 每日邮件发送次数限制
     * 同一邮箱每个自然天最多发送的重置邮件数量
     */
    private Integer dailySendLimit;

    /**
     * SMTP服务器地址
     */
    private String smtpHost;

    /**
     * SMTP服务器端口
     */
    private Integer smtpPort;

    /**
     * SMTP用户名（邮箱地址）
     */
    private String smtpUserName;

    /**
     * SMTP密码
     */
    private String smtpPassword;

    /**
     * 是否启用STARTTLS
     */
    private Boolean smtpStarttlsEnable;

    /**
     * 是否需要认证
     */
    private Boolean smtpAuth;

    /**
     * 配置验证
     */
    @PostConstruct
    public void validateConfig() {
        if (enabled) {
            log.info("邮件服务已启用 - SMTP: {}:{}", smtpHost, smtpPort);

            // 验证必要配置
            if (!StringUtils.hasText(smtpPassword)) {
                log.error("SMTP密码未配置！请设置环境变量 SMTP_PASSWORD");
            }

            if (!StringUtils.hasText(smtpUserName)) {
                log.error("SMTP用户名未配置！");
            }
        } else {
            log.info("邮件服务已禁用");
        }
    }

    /**
     * 配置JavaMailSender Bean
     */
    @Bean
    public JavaMailSender javaMailSender() {
        if (!enabled) {
            log.warn("邮件服务已禁用，返回空的JavaMailSender");
            // 返回空配置，避免启动失败
            return new JavaMailSenderImpl();
        }

        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();

        // 基本SMTP配置
        mailSender.setHost(smtpHost);
        mailSender.setPort(smtpPort);
        mailSender.setUsername(smtpUserName);
        mailSender.setPassword(smtpPassword);

        // SMTP属性配置 - 使用常量统一管理
        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", smtpAuth.toString());

        // STARTTLS配置（端口587）
        props.put("mail.smtp.starttls.enable", smtpStarttlsEnable.toString());
        props.put("mail.smtp.starttls.required", "true");

        // 超时配置 - 使用常量
        props.put("mail.smtp.connectiontimeout", EmailConstants.SMTP_CONNECTION_TIMEOUT);
        props.put("mail.smtp.timeout", EmailConstants.SMTP_READ_TIMEOUT);
        props.put("mail.smtp.writetimeout", EmailConstants.SMTP_WRITE_TIMEOUT);

        // SSL/TLS配置
        props.put("mail.smtp.ssl.trust", smtpHost);
        props.put("mail.smtp.ssl.protocols", "TLSv1.2");
        props.put("mail.smtp.ssl.checkserveridentity", "false");

        // 连接池配置 - 使用常量
        props.put("mail.smtp.connectionpoolsize", EmailConstants.SMTP_CONNECTION_POOL_SIZE);
        props.put("mail.smtp.connectionpooltimeout", EmailConstants.SMTP_CONNECTION_POOL_TIMEOUT);

        return mailSender;
    }
}
