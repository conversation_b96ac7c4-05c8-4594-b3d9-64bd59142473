package com.fixguru.utils;

import com.fixguru.constants.SystemConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * 环境判断工具类
 * 基于AWS Lambda环境变量进行精确环境判断，不再依赖Spring Profile
 * 提供统一的异步任务等待方法，避免代码重复
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public final class EnvironmentUtils {

    private EnvironmentUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 判断是否在AWS Lambda环境中运行
     * 基于AWS Lambda特有的环境变量进行判断
     *
     * @return 是否在AWS Lambda环境中
     */
    public static boolean isAwsLambdaEnvironment() {
        String functionName = System.getenv(SystemConstants.AWS_LAMBDA_FUNCTION_NAME);
        String runtimeDir = System.getenv(SystemConstants.LAMBDA_RUNTIME_DIR);

        boolean isLambda = functionName != null || runtimeDir != null;
        log.debug("AWS Lambda环境检查: functionName={}, runtimeDir={}, isLambda={}",
                functionName != null ? "存在" : "不存在",
                runtimeDir != null ? "存在" : "不存在",
                isLambda);

        return isLambda;
    }

    /**
     * 获取当前运行环境
     * 优先级：AWS Lambda环境变量 > SPRING_PROFILES_ACTIVE > 系统属性 > 默认local
     *
     * @return 当前环境名称
     */
    public static String getCurrentEnvironment() {
        // 1. 优先从环境变量获取（AWS Lambda环境）
        String profile = System.getenv(SystemConstants.SPRING_PROFILES_ACTIVE);
        if (profile != null && !profile.trim().isEmpty()) {
            log.debug("从环境变量获取环境: {}", profile);
            return profile.trim();
        }

        // 2. 从系统属性获取（本地开发环境）
        profile = System.getProperty("spring.profiles.active");
        if (profile != null && !profile.trim().isEmpty()) {
            log.debug("从系统属性获取环境: {}", profile);
            return profile.trim();
        }

        // 3. 默认为local环境
        log.debug("使用默认环境: {}", SystemConstants.ENV_LOCAL);
        return SystemConstants.ENV_LOCAL;
    }

    /**
     * 判断是否为生产环境
     *
     * @return 是否为生产环境
     */
    public static boolean isProductionEnvironment() {
        return SystemConstants.ENV_PROD.equals(getCurrentEnvironment());
    }

    /**
     * 判断是否为开发环境
     *
     * @return 是否为开发环境
     */
    public static boolean isDevelopmentEnvironment() {
        String env = getCurrentEnvironment();
        return SystemConstants.ENV_DEV.equals(env) || SystemConstants.ENV_LOCAL.equals(env);
    }

    /**
     * 判断是否为测试环境
     *
     * @return 是否为测试环境
     */
    public static boolean isTestEnvironment() {
        return SystemConstants.ENV_TEST.equals(getCurrentEnvironment());
    }

    /**
     * 判断是否为AWS云环境（dev/test/prod）
     * 区别于本地开发环境
     *
     * @return 是否为AWS云环境
     */
    public static boolean isAwsCloudEnvironment() {
        String env = getCurrentEnvironment();
        return SystemConstants.ENV_DEV.equals(env) ||
                SystemConstants.ENV_TEST.equals(env) ||
                SystemConstants.ENV_PROD.equals(env);
    }

    /**
     * 统一的异步任务等待方法（带任务描述）
     * AWS Lambda环境需要短暂等待，确保异步任务有时间执行
     * 本地环境不需要等待
     *
     * @param taskDescription 任务描述，用于日志记录
     */
    public static void waitForAsyncTaskIfNeeded(String taskDescription) {
        if (isAwsLambdaEnvironment() || isAwsCloudEnvironment()) {
            try {
                Thread.sleep(SystemConstants.ASYNC_TASK_WAIT_MS);
                log.debug("AWS环境{}等待完成: {}ms", taskDescription, SystemConstants.ASYNC_TASK_WAIT_MS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("{}等待被中断", taskDescription, e);
            }
        } else {
            log.debug("本地环境，跳过{}等待", taskDescription);
        }
    }

    /**
     * 获取环境详细信息
     * 用于调试和监控
     *
     * @return 环境详细信息
     */
    public static String getEnvironmentInfo() {
        String currentEnv = getCurrentEnvironment();
        boolean isLambda = isAwsLambdaEnvironment();
        boolean isCloud = isAwsCloudEnvironment();

        return String.format("Environment: %s, AWS Lambda: %s, AWS Cloud: %s",
                currentEnv, isLambda, isCloud);
    }
}
