package com.fixguru.utils;

import com.fixguru.config.JwtConfig;
import com.fixguru.constants.SystemConstants;
import com.fixguru.dto.UserInfoDTO;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.exception.BizException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 提供JWT令牌的生成、验证和解析功能
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtUtils {

    private static final String TOKEN_TYPE_SEPARATOR = "_";
    private final JwtConfig jwtConfig;

    /**
     * 获取签名密钥
     */
    private SecretKey getSigningKey() {
        return jwtConfig.getSigningKey();
    }

    /**
     * 生成Access Token
     *
     * @param userInfo 用户信息
     * @return JWT令牌
     */
    public String generateAccessToken(UserInfoDTO userInfo) {
        return generateToken(userInfo, jwtConfig.getAccessTokenExpirationSeconds(), SystemConstants.JWT_TOKEN_TYPE_ACCESS);
    }

    /**
     * 生成Refresh Token
     *
     * @param userInfo 用户信息
     * @return JWT令牌
     */
    public String generateRefreshToken(UserInfoDTO userInfo) {
        return generateToken(userInfo, jwtConfig.getRefreshTokenExpirationSeconds(), SystemConstants.JWT_TOKEN_TYPE_REFRESH);
    }

    /**
     * 生成重置密码令牌
     *
     * @param userInfo 用户信息
     * @param expirationMinutes 过期时间（分钟）
     * @return JWT令牌
     */
    public String generateResetPasswordToken(UserInfoDTO userInfo, int expirationMinutes) {
        return generateResetToken(userInfo, expirationMinutes * 60L, SystemConstants.JWT_TOKEN_TYPE_RESET);
    }

    /**
     * 生成激活令牌
     *
     * @param userInfo 用户信息
     * @param expirationHours 过期时间（小时）
     * @return JWT令牌
     */
    public String generateActivateToken(UserInfoDTO userInfo, int expirationHours) {
        return generateSpecialToken(userInfo, expirationHours * 3600L, SystemConstants.JWT_TOKEN_TYPE_ACTIVATE, SystemConstants.JWT_PURPOSE_ACCOUNT_ACTIVATE);
    }

    /**
     * 生成JWT令牌
     *
     * @param userInfo          用户信息
     * @param expirationSeconds 过期时间（秒）
     * @param tokenType         令牌类型
     * @return JWT令牌
     */
    private String generateToken(UserInfoDTO userInfo, long expirationSeconds, String tokenType) {
        Instant now = Instant.now();
        Instant expiration = now.plusSeconds(expirationSeconds);

        Map<String, Object> claims = new HashMap<>();
        claims.put(SystemConstants.JWT_CLAIM_USER_ID, userInfo.getUserId());
        claims.put(SystemConstants.JWT_CLAIM_EMAIL, userInfo.getEmail());
        claims.put(SystemConstants.JWT_CLAIM_USERNAME, userInfo.getUserName());
        // 添加登录时间戳，用于登出验证
        claims.put(SystemConstants.JWT_CLAIM_LOGIN_TIME, now.getEpochSecond());

        return tokenType + TOKEN_TYPE_SEPARATOR + Jwts.builder()
                .claims(claims)
                .subject(SystemConstants.JWT_SUBJECT)
                .issuedAt(Date.from(now))
                .expiration(Date.from(expiration))
                .signWith(getSigningKey())
                .compact();
    }

    /**
     * 生成重置密码专用令牌
     *
     * @param userInfo          用户信息
     * @param expirationSeconds 过期时间（秒）
     * @param tokenType         令牌类型
     * @return JWT令牌
     */
    private String generateResetToken(UserInfoDTO userInfo, long expirationSeconds, String tokenType) {
        Instant now = Instant.now();
        Instant expiration = now.plusSeconds(expirationSeconds);

        Map<String, Object> claims = new HashMap<>();
        claims.put(SystemConstants.JWT_CLAIM_USER_ID, userInfo.getUserId());
        claims.put(SystemConstants.JWT_CLAIM_EMAIL, userInfo.getEmail());
        claims.put(SystemConstants.JWT_CLAIM_USERNAME, userInfo.getUserName());
        // 重置令牌添加特殊标识和时间戳
        claims.put(SystemConstants.JWT_CLAIM_RESET_TIME, now.getEpochSecond());
        claims.put(SystemConstants.JWT_CLAIM_PURPOSE, SystemConstants.JWT_PURPOSE_PASSWORD_RESET);

        return tokenType + TOKEN_TYPE_SEPARATOR + Jwts.builder()
                .claims(claims)
                .subject(SystemConstants.JWT_SUBJECT_RESET)
                .issuedAt(Date.from(now))
                .expiration(Date.from(expiration))
                .signWith(getSigningKey())
                .compact();
    }

    /**
     * 生成特殊用途令牌（激活、重置等）
     *
     * @param userInfo          用户信息
     * @param expirationSeconds 过期时间（秒）
     * @param tokenType         令牌类型
     * @param purpose           令牌用途
     * @return JWT令牌
     */
    private String generateSpecialToken(UserInfoDTO userInfo, long expirationSeconds, String tokenType, String purpose) {
        Instant now = Instant.now();
        Instant expiration = now.plusSeconds(expirationSeconds);

        Map<String, Object> claims = new HashMap<>();
        claims.put(SystemConstants.JWT_CLAIM_USER_ID, userInfo.getUserId());
        claims.put(SystemConstants.JWT_CLAIM_EMAIL, userInfo.getEmail());
        claims.put(SystemConstants.JWT_CLAIM_USERNAME, userInfo.getUserName());
        claims.put(SystemConstants.JWT_CLAIM_ISSUE_TIME, now.getEpochSecond());
        claims.put(SystemConstants.JWT_CLAIM_PURPOSE, purpose);

        String subject = getSubjectByTokenType(tokenType);
        return tokenType + TOKEN_TYPE_SEPARATOR + Jwts.builder()
                .claims(claims)
                .subject(subject)
                .issuedAt(Date.from(now))
                .expiration(Date.from(expiration))
                .signWith(getSigningKey())
                .compact();
    }

    /**
     * 根据token类型获取对应的subject
     *
     * @param tokenType token类型
     * @return JWT subject
     */
    private String getSubjectByTokenType(String tokenType) {
        return switch (tokenType) {
            case SystemConstants.JWT_TOKEN_TYPE_ACCESS -> SystemConstants.JWT_SUBJECT_ACCESS;
            case SystemConstants.JWT_TOKEN_TYPE_REFRESH -> SystemConstants.JWT_SUBJECT_REFRESH;
            case SystemConstants.JWT_TOKEN_TYPE_RESET -> SystemConstants.JWT_SUBJECT_RESET;
            case SystemConstants.JWT_TOKEN_TYPE_ACTIVATE -> SystemConstants.JWT_SUBJECT_ACTIVATE;
            default -> SystemConstants.JWT_SUBJECT_UNKNOWN;
        };
    }

    /**
     * 验证JWT令牌
     *
     * @param token JWT令牌
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            String actualToken = extractActualToken(token);
            if (actualToken == null) {
                return false;
            }

            Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(actualToken);
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("JWT令牌验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 提取实际的JWT令牌（去除前缀）
     *
     * @param token 带前缀的令牌
     * @return 实际的JWT令牌
     */
    private String extractActualToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return null;
        }

        // 检查是否有access_、refresh_、reset_或activate_前缀
        if (token.startsWith(SystemConstants.JWT_TOKEN_PREFIX_ACCESS)) {
            return token.substring(SystemConstants.JWT_TOKEN_PREFIX_ACCESS.length());
        } else if (token.startsWith(SystemConstants.JWT_TOKEN_PREFIX_REFRESH)) {
            return token.substring(SystemConstants.JWT_TOKEN_PREFIX_REFRESH.length());
        } else if (token.startsWith(SystemConstants.JWT_TOKEN_PREFIX_RESET)) {
            return token.substring(SystemConstants.JWT_TOKEN_PREFIX_RESET.length());
        } else if (token.startsWith(SystemConstants.JWT_TOKEN_PREFIX_ACTIVATE)) {
            return token.substring(SystemConstants.JWT_TOKEN_PREFIX_ACTIVATE.length());
        }

        // 如果没有前缀，直接返回原token（向后兼容）
        return token;
    }

    /**
     * 从JWT令牌中获取用户ID
     *
     * @param token JWT令牌
     * @return 用户ID
     */
    public String getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.get(SystemConstants.JWT_CLAIM_USER_ID, String.class) : null;
    }

    /**
     * 从JWT令牌中获取用户信息
     *
     * @param token JWT令牌
     * @return 用户信息
     */
    public UserInfoDTO getUserInfoFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        if (claims == null) {
            return null;
        }

        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setUserId(claims.get(SystemConstants.JWT_CLAIM_USER_ID, String.class));
        userInfo.setEmail(claims.get(SystemConstants.JWT_CLAIM_EMAIL, String.class));
        userInfo.setUserName(claims.get(SystemConstants.JWT_CLAIM_USERNAME, String.class));
        return userInfo;
    }

    /**
     * 获取令牌过期时间
     *
     * @param token JWT令牌
     * @return 过期时间
     */
    public Instant getExpirationFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.getExpiration().toInstant() : null;
    }

    /**
     * 获取token中的登录时间
     *
     * @param token JWT令牌
     * @return 登录时间
     */
    public Instant getLoginTimeFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        if (claims == null) {
            return null;
        }
        Long loginTimeSeconds = claims.get(SystemConstants.JWT_CLAIM_LOGIN_TIME, Long.class);
        return loginTimeSeconds != null ? Instant.ofEpochSecond(loginTimeSeconds) : null;
    }

    /**
     * 检查令牌是否需要刷新
     *
     * @param token JWT令牌
     * @return 是否需要刷新
     */
    public boolean shouldRefreshToken(String token) {
        Instant expiration = getExpirationFromToken(token);
        if (expiration == null) {
            return false;
        }

        Instant now = Instant.now();
        long secondsUntilExpiration = expiration.getEpochSecond() - now.getEpochSecond();
        return secondsUntilExpiration <= SystemConstants.TOKEN_REFRESH_THRESHOLD_SECONDS;
    }

    /**
     * 从JWT令牌中获取令牌类型
     *
     * @param token JWT令牌
     * @return 令牌类型（access/refresh）
     */
    public String getTokenTypeFromToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return null;
        }

        if (token.startsWith(SystemConstants.JWT_TOKEN_PREFIX_ACCESS)) {
            return SystemConstants.JWT_TOKEN_TYPE_ACCESS;
        } else if (token.startsWith(SystemConstants.JWT_TOKEN_PREFIX_REFRESH)) {
            return SystemConstants.JWT_TOKEN_TYPE_REFRESH;
        } else if (token.startsWith(SystemConstants.JWT_TOKEN_PREFIX_RESET)) {
            return SystemConstants.JWT_TOKEN_TYPE_RESET;
        } else if (token.startsWith(SystemConstants.JWT_TOKEN_PREFIX_ACTIVATE)) {
            return SystemConstants.JWT_TOKEN_TYPE_ACTIVATE;
        }

        return null;
    }

    /**
     * 从JWT令牌中获取Claims
     *
     * @param token JWT令牌
     * @return Claims
     */
    private Claims getClaimsFromToken(String token) {
        try {
            String actualToken = extractActualToken(token);
            if (actualToken == null) {
                return null;
            }

            return Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(actualToken)
                    .getPayload();
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("解析JWT令牌失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从HTTP请求头中提取access token
     *
     * @param request HTTP请求对象
     * @return access token
     * @throws BizException 如果Authorization头缺失
     */
    public static String extractAccessTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader(SystemConstants.SECURITY_AUTHORIZATION_HEADER);
        if (authHeader == null || authHeader.trim().isEmpty()) {
            log.warn("认证失败: 缺少Authorization头 - URI: {}, Method: {}", request.getRequestURI(), request.getMethod());
            throw new BizException(BizCodeEnum.UNAUTHORIZED, "Missing Authorization header");
        }
        return authHeader.trim();
    }

    /**
     * 从HTTP请求头中提取refresh token
     *
     * @param request HTTP请求对象
     * @return refresh token
     * @throws BizException 如果Authorization头缺失
     */
    public static String extractRefreshTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader(SystemConstants.SECURITY_AUTHORIZATION_HEADER);
        if (authHeader == null || authHeader.trim().isEmpty()) {
            log.warn("刷新令牌失败: 缺少Authorization头");
            throw new BizException(BizCodeEnum.REFRESH_TOKEN_INVALID, "Missing refresh token in Authorization header");
        }
        return authHeader.trim();
    }

    /**
     * 验证重置密码令牌
     *
     * @param resetToken 重置令牌
     * @return 是否有效
     */
    public boolean validateResetPasswordToken(String resetToken) {
        try {
            // 检查令牌类型
            String tokenType = getTokenTypeFromToken(resetToken);
            if (!SystemConstants.JWT_TOKEN_TYPE_RESET.equals(tokenType)) {
                log.warn("重置令牌类型错误: 期望reset，实际为{}", tokenType);
                return false;
            }

            // 验证令牌基本有效性
            if (!validateToken(resetToken)) {
                return false;
            }

            // 检查令牌用途
            Claims claims = getClaimsFromToken(resetToken);
            if (claims == null) {
                return false;
            }

            String purpose = claims.get(SystemConstants.JWT_CLAIM_PURPOSE, String.class);
            if (!SystemConstants.JWT_PURPOSE_PASSWORD_RESET.equals(purpose)) {
                log.warn("重置令牌用途错误: 期望{}，实际为{}", SystemConstants.JWT_PURPOSE_PASSWORD_RESET, purpose);
                return false;
            }

            return true;
        } catch (Exception e) {
            log.warn("重置令牌验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从重置令牌中获取重置时间
     *
     * @param resetToken 重置令牌
     * @return 重置时间
     */
    public Instant getResetTimeFromToken(String resetToken) {
        Claims claims = getClaimsFromToken(resetToken);
        if (claims == null) {
            return null;
        }
        Long resetTimeSeconds = claims.get(SystemConstants.JWT_CLAIM_RESET_TIME, Long.class);
        return resetTimeSeconds != null ? Instant.ofEpochSecond(resetTimeSeconds) : null;
    }

    /**
     * 验证激活令牌
     *
     * @param activateToken 激活令牌
     * @return 是否有效
     */
    public boolean validateActivateToken(String activateToken) {
        try {
            // 检查令牌类型
            String tokenType = getTokenTypeFromToken(activateToken);
            if (!SystemConstants.JWT_TOKEN_TYPE_ACTIVATE.equals(tokenType)) {
                log.warn("激活令牌类型错误: 期望activate，实际为{}", tokenType);
                return false;
            }

            // 验证令牌基本有效性
            if (!validateToken(activateToken)) {
                return false;
            }

            // 检查令牌用途
            Claims claims = getClaimsFromToken(activateToken);
            if (claims == null) {
                return false;
            }

            String purpose = claims.get(SystemConstants.JWT_CLAIM_PURPOSE, String.class);
            if (!SystemConstants.JWT_PURPOSE_ACCOUNT_ACTIVATE.equals(purpose)) {
                log.warn("激活令牌用途错误: 期望{}，实际为{}", SystemConstants.JWT_PURPOSE_ACCOUNT_ACTIVATE, purpose);
                return false;
            }
            return true;

        } catch (Exception e) {
            log.error("激活令牌验证异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从激活令牌中获取用户ID
     *
     * @param activateToken 激活令牌
     * @return 用户ID
     */
    public String getUserIdFromActivateToken(String activateToken) {
        Claims claims = getClaimsFromToken(activateToken);
        if (claims == null) {
            return null;
        }
        return claims.get(SystemConstants.JWT_CLAIM_USER_ID, String.class);
    }

    /**
     * 从HTTP请求中提取Reset Token
     *
     * @param request HTTP请求
     * @return Reset Token，如果不存在则返回null
     */
    public static String extractResetTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader(SystemConstants.SECURITY_AUTHORIZATION_HEADER);
        if (authHeader != null && authHeader.startsWith(SystemConstants.JWT_TOKEN_PREFIX_RESET)) {
            return authHeader;
        }
        return null;
    }

    /**
     * 从HTTP请求中提取Activate Token
     *
     * @param request HTTP请求
     * @return Activate Token，如果不存在则返回null
     */
    public static String extractActivateTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader(SystemConstants.SECURITY_AUTHORIZATION_HEADER);
        if (authHeader != null && authHeader.startsWith(SystemConstants.JWT_TOKEN_PREFIX_ACTIVATE)) {
            return authHeader;
        }
        return null;
    }
}
