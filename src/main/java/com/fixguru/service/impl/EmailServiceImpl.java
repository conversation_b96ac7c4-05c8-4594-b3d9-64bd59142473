package com.fixguru.service.impl;

import com.fixguru.config.EmailConfig;
import com.fixguru.constants.SystemConstants;
import com.fixguru.service.EmailService;
import com.fixguru.utils.EnvironmentUtils;
import com.fixguru.utils.SmtpDiagnosticUtils;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 邮件服务实现类
 * 使用SMTP发送邮件（支持Zoho Mail）
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmailServiceImpl implements EmailService {

    private final EmailConfig emailConfig;
    private final JavaMailSender mailSender;

    /**
     * 统一的前端域名
     */
    private static final String FRONTEND_DOMAIN = "https://fixguruapp.com";

    /**
     * 邮件发送时间记录（用于频率限制）
     * 在生产环境中，这应该存储在Redis或数据库中
     */
    private final ConcurrentHashMap<String, Instant> emailSentTimes = new ConcurrentHashMap<>();

    /**
     * 每日邮件发送次数记录（用于每日限制）
     * Key: email + ":" + date (yyyy-MM-dd), Value: 发送次数
     * 在生产环境中，这应该存储在Redis或数据库中
     */
    private final ConcurrentHashMap<String, Integer> dailySendCounts = new ConcurrentHashMap<>();

    @Override
    @Async("emailTaskExecutor")
    public void sendPasswordResetEmail(String toEmail, String resetToken, String userName) {
        log.info("开始异步发送重置密码邮件: toEmail={}, userName={}", toEmail, userName);

        if (!emailConfig.getEnabled()) {
            log.info("邮件服务已禁用，跳过发送: toEmail={}", toEmail);
            return;
        }

        try {
            // 构建邮件内容
            String subject = emailConfig.getResetPasswordSubject();
            String htmlBody = buildResetPasswordEmailHtml(resetToken, userName);
            String textBody = buildResetPasswordEmailText(resetToken, userName);

            // 发送邮件
            boolean success = sendEmailInternal(toEmail, subject, htmlBody, textBody);

            if (success) {
                log.info("重置密码邮件发送成功: toEmail={}, userName={}", toEmail, userName);
            } else {
                log.error("重置密码邮件发送失败: toEmail={}, userName={}", toEmail, userName);
            }

        } catch (Exception e) {
            log.error("异步发送重置密码邮件异常: toEmail={}, userName={}, error={}",
                    toEmail, userName, e.getMessage(), e);
        }
    }

    @Override
    @Async("emailTaskExecutor")
    public void sendActivateEmail(String toEmail, String activateToken, String userName) {
        log.info("开始异步发送激活邮件: toEmail={}, userName={}", toEmail, userName);

        if (!emailConfig.getEnabled()) {
            log.info("邮件服务已禁用，跳过发送: toEmail={}", toEmail);
            return;
        }

        try {
            // 构建邮件内容
            String subject = emailConfig.getActivateEmailSubject();
            String htmlBody = buildActivateEmailHtml(activateToken, userName);
            String textBody = buildActivateEmailText(activateToken, userName);

            // 发送邮件
            boolean success = sendEmailInternal(toEmail, subject, htmlBody, textBody);

            if (success) {
                log.info("激活邮件发送成功: toEmail={}, userName={}", toEmail, userName);
            } else {
                log.error("激活邮件发送失败: toEmail={}, userName={}", toEmail, userName);
            }

        } catch (Exception e) {
            log.error("异步发送激活邮件异常: toEmail={}, userName={}, error={}",
                    toEmail, userName, e.getMessage(), e);
        }
    }

    @Override
    public boolean canSendResetEmail(String email) {
        Instant lastSentTime = emailSentTimes.get(email);
        if (lastSentTime == null) {
            return true;
        }

        Instant now = Instant.now();
        long minutesSinceLastSent = (now.getEpochSecond() - lastSentTime.getEpochSecond()) / 60;

        return minutesSinceLastSent >= emailConfig.getSendRateLimitMinutes();
    }

    @Override
    public void recordEmailSentTime(String email) {
        emailSentTimes.put(email, Instant.now());

        // 清理过期记录（避免内存泄漏）
        cleanupExpiredRecords();
    }

    @Override
    public boolean canSendDailyLimit(String email) {
        String todayKey = getDailyKey(email);
        Integer todayCount = dailySendCounts.getOrDefault(todayKey, 0);

        return todayCount < emailConfig.getDailySendLimit();
    }

    @Override
    public void recordDailySendCount(String email) {
        String todayKey = getDailyKey(email);
        dailySendCounts.merge(todayKey, 1, Integer::sum);

        // 清理过期的每日记录（避免内存泄漏）
        cleanupExpiredDailyRecords();
    }

    /**
     * 生成每日记录的Key
     * 格式: email:yyyy-MM-dd
     */
    private String getDailyKey(String email) {
        LocalDate today = LocalDate.now(ZoneId.systemDefault());
        return email + ":" + today.toString();
    }

    /**
     * 发送邮件（内部方法）
     * 增强错误处理和重试机制
     */
    private boolean sendEmailInternal(String toEmail, String subject, String htmlBody, String textBody) {
        int maxRetries = 3;
        // 2秒重试间隔
        int retryDelay = 2000;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                MimeMessage message = mailSender.createMimeMessage();
                MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

                // 设置邮件基本信息
                helper.setFrom(emailConfig.getFromEmail(), emailConfig.getFromName());
                helper.setTo(toEmail);
                helper.setSubject(subject);

                // 设置邮件内容（HTML优先显示，纯文本作为备用）
                helper.setText(textBody, htmlBody);

                // 发送邮件
                mailSender.send(message);
                log.info("SMTP邮件发送成功: toEmail={}, 尝试次数: {}", toEmail, attempt);

                return true;

            } catch (MessagingException e) {
                log.error("邮件构建失败: toEmail={}, 尝试次数: {}, error={}", toEmail, attempt, e.getMessage(), e);
                if (attempt == maxRetries) {
                    // 最后一次尝试失败，执行诊断
                    performSmtpDiagnostic();
                }
                return false;

            } catch (Exception e) {
                log.error("SMTP邮件发送失败: toEmail={}, 尝试次数: {}, error={}", toEmail, attempt, e.getMessage(), e);

                if (attempt < maxRetries) {
                    log.info("邮件发送失败，{}ms后进行第{}次重试", retryDelay, attempt + 1);
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.warn("重试等待被中断");
                        break;
                    }
                } else {
                    // 最后一次尝试失败，执行诊断
                    performSmtpDiagnostic();
                }
            }
        }

        return false;
    }

    /**
     * 执行SMTP连接诊断
     * 当邮件发送失败时自动诊断连接问题
     */
    private void performSmtpDiagnostic() {
        try {
            log.info("邮件发送失败，开始SMTP连接诊断...");
            boolean quickTest = SmtpDiagnosticUtils.quickTestSmtpConnection(
                emailConfig.getSmtpHost(),
                emailConfig.getSmtpPort()
            );

            if (!quickTest) {
                log.error("SMTP连接诊断失败: 无法连接到 {}:{}",
                    emailConfig.getSmtpHost(), emailConfig.getSmtpPort());
                log.error("建议检查: 1)网络连接 2)防火墙设置 3)SMTP服务器状态 4)端口配置");
            } else {
                log.warn("SMTP连接正常，可能是认证问题，请检查用户名和密码配置");
            }
        } catch (Exception e) {
            log.debug("SMTP诊断执行异常: {}", e.getMessage());
        }
    }

    /**
     * 构建重置密码邮件HTML内容
     */
    private String buildResetPasswordEmailHtml(String resetToken, String userName) {
        String resetUrl = buildFrontendUrl("/reset-password", resetToken);

        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Reset Your Password</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        margin: 0;
                        padding: 20px;
                        background-color: #f5f5f5;
                    }
                    .container {
                        max-width: 600px;
                        margin: 0 auto;
                        background-color: #ffffff;
                        border-radius: 8px;
                        overflow: hidden;
                    }
                    .header {
                        background: #ffffff;
                        color: #333;
                        padding: 30px;
                        text-align: center;
                        border-bottom: 1px solid #e0e0e0;
                    }
                    .header h1 {
                        margin: 0;
                        font-size: 24px;
                        color: #333;
                    }
                    .content {
                        padding: 30px;
                    }
                    .userName, .company-name-large {
                        font-size: 18px;
                        font-weight: 700;
                        color: #333;
                    }
                    .button {
                        display: inline-block;
                        background: #5b9bd5;
                        color: white;
                        padding: 12px 30px;
                        text-decoration: none;
                        border-radius: 5px;
                        margin: 20px 0;
                    }

                    .support-email {
                        color: #4fc3f7;
                        text-decoration: none;
                    }
                    .divider {
                        border-top: 1px solid #e0e0e0;
                        margin: 30px 0;
                    }
                    .footer-text {
                        color: #999;
                        font-size: 14px;
                        text-align: center;
                        line-height: 1.5;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Reset Your Password</h1>
                    </div>
                    <div class="content">
                        <p>Hi <span class="userName">%s</span>,</p>

                        <p>We received a request to reset your password for your <span class="company-name-large">FixGuru</span> account. If you made this request, click the button below to reset your password:</p>

                        <div style="text-align: center;">
                            <a href="%s" class="button">Reset Password</a>
                        </div>

                        <p>If you have any questions, please contact our support team: <a href="mailto:<EMAIL>" class="support-email"><EMAIL></a></p>

                        <p>Best regards,<br>The FixGuru Team</p>

                        <div class="divider"></div>

                        <div class="footer-text">
                            If you did not make this request, you can safely ignore this email. Never share this reset link with anyone – FixGuru support will never ask for your reset link. Your account remains secure and no action is needed.
                        </div>
                    </div>
                </div>
            </body>
            </html>
            """,
            userName, resetUrl);
    }

    /**
     * 构建重置密码邮件纯文本内容
     */
    private String buildResetPasswordEmailText(String resetToken, String userName) {
        String resetUrl = buildFrontendUrl("/reset-password", resetToken);

        return String.format("""
            Hi %s,
            
            We received a request to reset your password for your FixGuru account.
            
            To reset your password, please visit the following link:
            %s
            
            IMPORTANT:
            - This link will expire in %d minutes
            - If you didn't request this reset, please ignore this email
            - For security, never share this link with anyone
            
            If you have any questions, please contact our support team: <EMAIL>

            Best regards,
            The FixGuru Team

            © 2025 FixGuru. All rights reserved.
            """,
            userName, resetUrl, emailConfig.getResetTokenExpirationMinutes());
    }

    /**
     * 清理过期的邮件发送记录
     */
    private void cleanupExpiredRecords() {
        // 保留2倍时间间隔，避免并发情况下的误删
        Instant cutoff = Instant.now().minusSeconds(emailConfig.getSendRateLimitMinutes() * 60L * 2);
        emailSentTimes.entrySet().removeIf(entry -> entry.getValue().isBefore(cutoff));
    }

    /**
     * 清理过期的每日发送记录
     */
    private void cleanupExpiredDailyRecords() {
        LocalDate yesterday = LocalDate.now(ZoneId.systemDefault()).minusDays(1);
        String yesterdayStr = yesterday.toString();

        // 移除昨天及更早的记录
        dailySendCounts.entrySet().removeIf(entry -> {
            String key = entry.getKey();
            int lastColonIndex = key.lastIndexOf(':');
            if (lastColonIndex > 0) {
                String dateStr = key.substring(lastColonIndex + 1);
                return dateStr.compareTo(yesterdayStr) <= 0;
            }
            return false;
        });
    }

    /**
     * 构建激活邮件HTML内容
     */
    private String buildActivateEmailHtml(String activateToken, String userName) {
        String activateUrl = buildFrontendUrl("/activate", activateToken);

        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Verify Your FixGuru Account</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { text-align: center; padding: 20px 0; border-bottom: 2px solid #e74c3c; }
                    .logo { font-size: 24px; font-weight: bold; color: #e74c3c; }
                    .content { padding: 30px 0; }
                    .userName, .company-name-large { font-size: 18px; font-weight: 700; color: #333; }
                    .number-highlight { font-size: 18px; font-weight: 700; color: #333; }
                    .button { display: inline-block; padding: 12px 30px; background-color: #e74c3c; color: white; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
                    .button:hover { background-color: #c0392b; }
                    .support-email { color: #4fc3f7; text-decoration: none; }
                    .footer { text-align: center; padding: 20px 0; border-top: 1px solid #eee; color: #666; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="logo">🔧 FixGuru</div>
                </div>

                <div class="content">
                    <h2>Verify Your Account</h2>
                    <p>Hi <span class="userName">%s</span>,</p>
                    <p>Welcome to <span class="company-name-large">FixGuru</span>! To start using all features, please verify your account by clicking the button below:</p>

                    <div style="text-align: center;">
                        <a href="%s" class="button">Verify Account</a>
                    </div>

                    <p>If you didn't create a FixGuru account, please ignore this email.</p>

                    <p><strong>Note:</strong> This verification link will expire in <span class="number-highlight">24</span> hours for security reasons.</p>
                </div>

                <div class="footer">
                    <p>Best regards,<br>The FixGuru Team<br><a href="mailto:<EMAIL>" class="support-email"><EMAIL></a></p>
                    <p>This is an automated message, please do not reply to this email.</p>
                </div>
            </body>
            </html>
            """, userName, activateUrl);
    }

    /**
     * 构建激活邮件纯文本内容
     */
    private String buildActivateEmailText(String activateToken, String userName) {
        String activateUrl = buildFrontendUrl("/activate", activateToken);

        return String.format("""
            Hi %s,

            Welcome to FixGuru! To start using all features, please verify your account.

            Click the following link to verify your account:
            %s

            If you didn't create a FixGuru account, please ignore this email.

            Note: This verification link will expire in 24 hours for security reasons.

            Best regards,
            The FixGuru Team
            <EMAIL>
            """, userName, activateUrl);
    }

    /**
     * 构建前端页面URL
     * 统一的前端域名，通过env参数区分不同环境
     *
     * @param path 页面路径（如 "/activate", "/reset-password"）
     * @param token 令牌
     * @return 完整的前端页面URL
     */
    private String buildFrontendUrl(String path, String token) {
        String currentEnv = EnvironmentUtils.getCurrentEnvironment();
        log.info("当前环境: {}", currentEnv);

        String frontendEnv;
        if (SystemConstants.ENV_LOCAL.equals(currentEnv) || SystemConstants.ENV_DEV.equals(currentEnv)) {
            // localhost 环境和 dev 环境均使用 dev 前端
            frontendEnv = SystemConstants.ENV_DEV;
        } else if (SystemConstants.ENV_PROD.equals(currentEnv)) {
            // prod 环境使用 prod 前端
            frontendEnv = SystemConstants.ENV_PROD;
        } else {
            // test 环境使用 test 前端（目前默认环境）
            frontendEnv = SystemConstants.ENV_TEST;
        }

        // 构建基础URL：统一域名 + 路径 + token参数
        String url = FRONTEND_DOMAIN + path + "?token=" + token;

        // 添加env参数，让前端知道应该调用哪个环境的后端API
        // 目前：不传参数默认test，未来prod上线后默认prod
        if (!SystemConstants.ENV_TEST.equals(frontendEnv)) {
            url += "&env=" + frontendEnv;
        }

        log.info("构建前端URL: path={}, url={}", path, url);
        return url;
    }

}
