AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: FixGuru - Spring Boot Lambda Application

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, test, prod]
    Description: Environment name (dev/test/prod)

  SmtpPassword:
    Type: String
    NoEcho: true
    Default: yiYJjDpFspSs
    Description: SMTP password for Zoho Mail (<EMAIL>)

Conditions:
  IsProdEnvironment: !Equals [!Ref Environment, prod]

Globals:
  Function:
    Timeout: 30
    MemorySize: 1024
    Runtime: java17
    Environment:
      Variables:
        SPRING_PROFILES_ACTIVE: lambda
        JAVA_TOOL_OPTIONS: -Djava.awt.headless=true -Xmx768m

Resources:
  # Lambda函数
  FixGuruFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'fixguru-function-${Environment}'
      CodeUri: .
      Handler: com.fixguru.aws.lambda.SpringBootLambdaHandler::handleRequest
      Description: FixGuru Spring Boot Lambda Function
      Environment:
        Variables:
          SPRING_PROFILES_ACTIVE: !Ref Environment
          ENVIRONMENT: !Ref Environment
          USER_TABLE_NAME: !Sub '${Environment}_User'
          AWS_DYNAMODB_TABLE_PREFIX: !Sub '${Environment}_'
          SMTP_PASSWORD: !Ref SmtpPassword
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Sub '${Environment}_User'
        - Statement:
            - Effect: Allow
              Action:
                - dynamodb:ListTables
                - dynamodb:DescribeTable
              Resource: "*"
        - CloudWatchLogsFullAccess
      Events:
        ApiEvent:
          Type: Api
          Properties:
            Path: /{proxy+}
            Method: ANY
            RestApiId: !Ref ApiGateway
        RootApiEvent:
          Type: Api
          Properties:
            Path: /
            Method: ANY
            RestApiId: !Ref ApiGateway

  # API Gateway (EDGE optimized for global CDN)
  ApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      Name: !Sub 'fixguru-api-${Environment}'
      StageName: api
      Description: FixGuru API Gateway with Global CDN
      Cors:
        AllowMethods: "'*'"
        AllowHeaders: "'*'"
        AllowOrigin: "'*'"
      EndpointConfiguration:
        Type: EDGE
      # EDGE configuration provides global CloudFront distribution

  # Global CDN Custom Domain (EDGE optimized)
  ApiCustomDomain:
    Type: AWS::ApiGateway::DomainName
    Properties:
      DomainName: !If
        - IsProdEnvironment
        - fixguruapp.com
        - !Sub '${Environment}.fixguruapp.com'
      CertificateArn: arn:aws:acm:us-east-1:383429079525:certificate/e6aeb554-7bc9-46ea-81a5-a4faeb973db7
      EndpointConfiguration:
        Types: [EDGE]

  # Path Mapping
  ApiPathMapping:
    Type: AWS::ApiGateway::BasePathMapping
    Properties:
      DomainName: !Ref ApiCustomDomain
      RestApiId: !Ref ApiGateway
      Stage: !Ref ApiGateway.Stage

  # Route53 Hosted Zone
  HostedZone:
    Type: AWS::Route53::HostedZone
    Properties:
      Name: fixguruapp.com
      HostedZoneConfig:
        Comment: !Sub 'Hosted zone for FixGuru ${Environment} environment'

  # DNS Record for Custom Domain (EDGE optimized)
  DNSRecord:
    Type: AWS::Route53::RecordSet
    Properties:
      HostedZoneId: !Ref HostedZone
      Name: !If
        - IsProdEnvironment
        - fixguruapp.com
        - !Sub '${Environment}.fixguruapp.com'
      Type: A
      AliasTarget:
        DNSName: !GetAtt ApiCustomDomain.DistributionDomainName
        HostedZoneId: !GetAtt ApiCustomDomain.DistributionHostedZoneId
        EvaluateTargetHealth: false

  # DynamoDB用户表
  UserTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub '${Environment}_User'
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
        - AttributeName: userName
          AttributeType: S
        - AttributeName: email
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: userName-index
          KeySchema:
            - AttributeName: userName
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: email-index
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Project
          Value: FixGuru

  # CloudWatch日志组
  FunctionLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/fixguru-function-${Environment}'
      RetentionInDays: 14

Outputs:
  ApiGatewayUrl:
    Description: API Gateway URL (Default)
    Value: !Sub 'https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/api'
    Export:
      Name: !Sub 'fixguru-api-url-${Environment}'

  CustomDomainUrl:
    Description: Custom Domain URL
    Value: !If
      - IsProdEnvironment
      - https://fixguruapp.com
      - !Sub 'https://${Environment}.fixguruapp.com'
    Export:
      Name: !Sub 'fixguru-custom-url-${Environment}'

  CustomDomainName:
    Description: Custom Domain Name
    Value: !Ref ApiCustomDomain
    Export:
      Name: !Sub 'fixguru-domain-${Environment}'

  DistributionDomainName:
    Description: CloudFront Distribution Domain Name
    Value: !GetAtt ApiCustomDomain.DistributionDomainName
    Export:
      Name: !Sub 'fixguru-distribution-domain-${Environment}'

  HostedZoneId:
    Description: Route53 Hosted Zone ID
    Value: !Ref HostedZone
    Export:
      Name: !Sub 'fixguru-hosted-zone-${Environment}'

  FunctionArn:
    Description: Lambda Function ARN
    Value: !GetAtt FixGuruFunction.Arn
    Export:
      Name: !Sub 'fixguru-function-arn-${Environment}'

  UserTableName:
    Description: DynamoDB User Table Name
    Value: !Sub '${Environment}_User'
    Export:
      Name: !Sub 'fixguru-user-table-${Environment}'
