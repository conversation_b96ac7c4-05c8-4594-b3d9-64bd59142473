package com.fixguru.service;

import com.fixguru.dto.AuthTokenDTO;
import com.fixguru.dto.UserInfoDTO;
import com.fixguru.dto.request.ForgotPasswordRequest;
import com.fixguru.dto.request.ResetPasswordRequest;
import com.fixguru.dto.request.UserCreateRequest;
import com.fixguru.dto.request.UserLoginRequest;

import java.time.Instant;
import java.util.Map;

/**
 * 用户服务接口
 * 提供用户相关的业务逻辑处理
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface UserService {


    /**
     * 创建用户
     *
     * @param request 用户创建请求
     * @return 用户信息
     */
    UserInfoDTO createUser(UserCreateRequest request);

    /**
     * 根据用户ID获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    UserInfoDTO getUserById(String userId);

    /**
     * 根据用户名获取用户信息
     *
     * @param userName 用户名
     * @return 用户信息
     */
    UserInfoDTO getUserByUserName(String userName);

    /**
     * 根据邮箱获取用户信息
     *
     * @param email 邮箱
     * @return 用户信息
     */
    UserInfoDTO getUserByEmail(String email);


    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 认证令牌
     */
    AuthTokenDTO login(UserLoginRequest request);

    /**
     * 忘记密码
     *
     * @param request 忘记密码请求
     */
    void forgotPassword(ForgotPasswordRequest request);

    /**
     * 重置密码
     *
     * @param resetToken 重置令牌
     * @param request 重置密码请求
     */
    void resetPassword(String resetToken, ResetPasswordRequest request);

    /**
     * 验证重置密码令牌
     *
     * @param resetToken 重置令牌
     * @return 是否有效
     */
    boolean validateResetToken(String resetToken);


    /**
     * 检查用户名是否存在
     *
     * @param userName 用户名
     * @return 是否存在
     */
    boolean existsByUserName(String userName);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查用户是否可以登录
     *
     * @param userId 用户ID
     * @return 是否可以登录
     */
    boolean canUserLogin(String userId);

    /**
     * 获取用户状态信息（带权限检查）
     * 用户只能查看自己的状态信息
     *
     * @param userId 用户ID
     * @return 用户状态信息
     */
    Map<String, Object> getUserStatus(String userId);


    /**
     * 锁定用户账户
     * 支持自动锁定（登录失败5次后锁定15分钟）和手动锁定
     *
     * @param userId 用户ID
     * @param reason 锁定原因
     * @return 是否锁定成功
     */
    boolean lockUser(String userId, String reason);

    /**
     * 解锁用户账户
     *
     * @param userId 用户ID
     * @param reason 解锁原因
     * @return 是否解锁成功
     */
    boolean unlockUser(String userId, String reason);

    /**
     * 禁用用户账户
     *
     * @param userId  用户ID
     * @param adminId 管理员ID
     * @param reason  禁用原因
     * @return 是否禁用成功
     */
    boolean disableUser(String userId, String adminId, String reason);

    /**
     * 启用用户账户
     *
     * @param userId  用户ID
     * @param adminId 管理员ID
     * @param reason  启用原因
     * @return 是否启用成功
     */
    boolean enableUser(String userId, String adminId, String reason);

    /**
     * 获取用户最后登出时间
     *
     * @param userId 用户ID
     * @return 最后登出时间，如果从未登出则返回null
     */
    Instant getUserLastLogoutTime(String userId);

    /**
     * 更新用户最后登出时间
     *
     * @param userId 用户ID
     * @param logoutTime 登出时间
     */
    void updateUserLastLogoutTime(String userId, Instant logoutTime);

    /**
     * 激活用户账户
     *
     * @param activateToken 激活令牌
     * @return 激活结果信息
     */
    UserInfoDTO activateUser(String activateToken);
}
