package com.fixguru.controller;

import com.fixguru.common.ApiResponse;
import com.fixguru.config.EmailConfig;
import com.fixguru.utils.EnvironmentUtils;
import com.fixguru.utils.SmtpDiagnosticUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.ListTablesRequest;
import software.amazon.awssdk.services.dynamodb.model.ListTablesResponse;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 提供系统健康状态检查接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/v1/health")
@Tag(name = "健康检查", description = "系统健康状态检查接口")
public class HealthController {

    private final DynamoDbClient dynamoDbClient;
    private final EmailConfig emailConfig;

    public HealthController(DynamoDbClient dynamoDbClient, EmailConfig emailConfig) {
        this.dynamoDbClient = dynamoDbClient;
        this.emailConfig = emailConfig;
    }

    @Value("${app.name:FixGuru}")
    private String appName;

    @Value("${app.version:1.0.0}")
    private String appVersion;

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Value("${aws.region:us-west-1}")
    private String awsRegion;

    @Value("${app.health-check.dynamodb.enabled:true}")
    private boolean dynamoDbHealthCheckEnabled;


    /**
     * 健康检查
     */
    @GetMapping
    @Operation(summary = "基础健康检查", description = "检查系统基本运行状态，无需认证，返回系统状态、版本信息等")
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", Instant.now());
        healthInfo.put("application", appName);
        healthInfo.put("version", appVersion);
        healthInfo.put("profile", activeProfile);
        healthInfo.put("uptime", getUptime());

        log.debug("健康检查请求");
        return ApiResponse.success("System is running normally", healthInfo);
    }


    /**
     * 详细健康检查
     */
    @GetMapping("/detailed")
    @Operation(summary = "详细健康检查", description = "详细的系统状态检查，包括AWS DynamoDB连接状态、内存使用情况等，无需认证")
    public ApiResponse<Map<String, Object>> detailedHealth() {
        Map<String, Object> detailedInfo = new HashMap<>();
        boolean overallStatus = true;

        // 基本信息
        detailedInfo.put("timestamp", Instant.now());
        detailedInfo.put("application", appName);
        detailedInfo.put("version", appVersion);
        detailedInfo.put("profile", activeProfile);
        detailedInfo.put("region", awsRegion);

        // 系统信息
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("processors", runtime.availableProcessors());
        systemInfo.put("totalMemory", runtime.totalMemory());
        systemInfo.put("freeMemory", runtime.freeMemory());
        systemInfo.put("maxMemory", runtime.maxMemory());
        systemInfo.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
        detailedInfo.put("system", systemInfo);

        // JVM信息
        Map<String, Object> jvmInfo = new HashMap<>();
        jvmInfo.put("version", System.getProperty("java.version"));
        jvmInfo.put("vendor", System.getProperty("java.vendor"));
        jvmInfo.put("home", System.getProperty("java.home"));
        detailedInfo.put("jvm", jvmInfo);

        // AWS服务健康检查
        Map<String, Object> awsServices = new HashMap<>();

        // DynamoDB健康检查
        if (dynamoDbHealthCheckEnabled) {
            Map<String, Object> dynamoDbStatus = checkDynamoDbHealth();
            awsServices.put("dynamodb", dynamoDbStatus);
            if (!"UP".equals(dynamoDbStatus.get("status"))) {
                overallStatus = false;
            }
        }

        detailedInfo.put("awsServices", awsServices);

        // 运行时间
        detailedInfo.put("uptime", getUptime());

        // 设置总体状态
        detailedInfo.put("status", overallStatus ? "UP" : "DOWN");

        log.debug("详细健康检查请求，总体状态: {}", overallStatus ? "UP" : "DOWN");
        return ApiResponse.success(overallStatus ? "System is running normally" : "System has issues", detailedInfo);
    }


    /**
     * 获取系统运行时间
     */
    private String getUptime() {
        long uptimeMillis = System.currentTimeMillis() - getStartTime();
        long seconds = uptimeMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        return String.format("%d days %d hours %d minutes %d seconds",
                days, hours % 24, minutes % 60, seconds % 60);
    }

    /**
     * 获取系统启动时间（简化处理）
     */
    private long getStartTime() {
        // 这里简化处理，实际项目中可以在应用启动时记录启动时间
        return System.currentTimeMillis() -
                java.lang.management.ManagementFactory.getRuntimeMXBean().getUptime();
    }

    /**
     * 检查DynamoDB健康状态
     */
    private Map<String, Object> checkDynamoDbHealth() {
        Map<String, Object> status = new HashMap<>();
        long startTime = System.currentTimeMillis();

        try {
            // 尝试列出表来验证连接
            ListTablesRequest request = ListTablesRequest.builder()
                    .limit(1)
                    .build();

            ListTablesResponse response = dynamoDbClient.listTables(request);
            long responseTime = System.currentTimeMillis() - startTime;

            status.put("status", "UP");
            status.put("responseTime", responseTime + "ms");
            status.put("tablesCount", response.tableNames().size());
            status.put("region", awsRegion);
            status.put("message", "DynamoDB connection is healthy");

            log.debug("DynamoDB健康检查成功，响应时间: {}ms", responseTime);

        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            status.put("status", "DOWN");
            status.put("responseTime", responseTime + "ms");
            status.put("error", e.getMessage());
            status.put("region", awsRegion);
            status.put("message", "DynamoDB connection failed");

            log.error("DynamoDB健康检查失败: {}", e.getMessage());
        }

        return status;
    }

    /**
     * 环境信息检查
     * 返回当前运行环境的详细信息，用于验证环境判断逻辑
     */
    @GetMapping("/environment")
    @Operation(summary = "环境信息检查", description = "返回当前运行环境的详细信息，包括环境名称、是否AWS Lambda环境等，无需认证")
    public ApiResponse<Map<String, Object>> environmentInfo() {
        Map<String, Object> envInfo = new HashMap<>();

        // 基本环境信息
        envInfo.put("timestamp", Instant.now());
        envInfo.put("currentEnvironment", EnvironmentUtils.getCurrentEnvironment());
        envInfo.put("isAwsLambdaEnvironment", EnvironmentUtils.isAwsLambdaEnvironment());
        envInfo.put("isAwsCloudEnvironment", EnvironmentUtils.isAwsCloudEnvironment());
        envInfo.put("isProductionEnvironment", EnvironmentUtils.isProductionEnvironment());
        envInfo.put("isDevelopmentEnvironment", EnvironmentUtils.isDevelopmentEnvironment());
        envInfo.put("isTestEnvironment", EnvironmentUtils.isTestEnvironment());

        // 环境变量信息（用于调试）
        Map<String, Object> envVars = new HashMap<>();
        envVars.put("SPRING_PROFILES_ACTIVE", System.getenv("SPRING_PROFILES_ACTIVE"));
        envVars.put("AWS_LAMBDA_FUNCTION_NAME", System.getenv("AWS_LAMBDA_FUNCTION_NAME") != null ? "存在" : "不存在");
        envVars.put("LAMBDA_RUNTIME_DIR", System.getenv("LAMBDA_RUNTIME_DIR") != null ? "存在" : "不存在");
        envVars.put("spring.profiles.active", System.getProperty("spring.profiles.active"));
        envInfo.put("environmentVariables", envVars);

        // 应用信息
        envInfo.put("application", appName);
        envInfo.put("version", appVersion);
        envInfo.put("profile", activeProfile);
        envInfo.put("region", awsRegion);

        // 详细信息字符串
        envInfo.put("environmentInfo", EnvironmentUtils.getEnvironmentInfo());

        log.debug("环境信息检查请求: {}", EnvironmentUtils.getEnvironmentInfo());
        return ApiResponse.success("Environment information retrieved successfully", envInfo);
    }

    /**
     * SMTP连接诊断
     * 诊断Zoho Mail连接状态和配置问题
     */
    @GetMapping("/smtp")
    @Operation(summary = "SMTP连接诊断", description = "诊断Zoho Mail连接状态，包括网络连接、协议握手和配置建议，无需认证")
    public ApiResponse<Map<String, Object>> smtpDiagnostic() {
        Map<String, Object> result = new HashMap<>();

        // 基本信息
        result.put("timestamp", Instant.now());
        result.put("smtpHost", emailConfig.getSmtpHost());
        result.put("smtpPort", emailConfig.getSmtpPort());
        result.put("emailEnabled", emailConfig.getEnabled());

        if (!emailConfig.getEnabled()) {
            result.put("status", "DISABLED");
            result.put("message", "邮件服务已禁用");
            return ApiResponse.success("SMTP service is disabled", result);
        }

        // 执行连接诊断
        Map<String, Object> diagnosticResult = SmtpDiagnosticUtils.diagnoseSmtpConnection(
            emailConfig.getSmtpHost(),
            emailConfig.getSmtpPort()
        );

        Map<String, Object> networkResult = (Map<String, Object>) diagnosticResult.getOrDefault("networkConnection", Map.of("success", false));
        Map<String, Object> smtpResult = (Map<String, Object>) diagnosticResult.getOrDefault("smtpHandshake", Map.of("success", false));

        boolean networkSuccess = (Boolean) networkResult.get("success");
        boolean smtpSuccess = (Boolean) smtpResult.get("success");
        boolean overallSuccess = networkSuccess && smtpSuccess;

        // 简化的诊断结果
        result.put("networkConnection", networkSuccess);
        result.put("smtpHandshake", smtpSuccess);
        result.put("overallSuccess", overallSuccess);

        if (!overallSuccess) {
            // 添加Zoho Mail特定建议
            result.put("troubleshooting", new String[]{
                "确认已使用Zoho应用专用密码而不是普通密码",
                "检查网络连接到smtp.zoho.com:587",
                "验证Zoho账户SMTP访问权限"
            });

            if (!networkSuccess) {
                result.put("issue", "网络连接失败");
            } else if (!smtpSuccess) {
                result.put("issue", "SMTP握手失败，可能是网络连接问题");
            }
        }

        result.put("status", overallSuccess ? "SUCCESS" : "FAILED");
        String message = overallSuccess ? "SMTP连接正常" : "SMTP连接存在问题";

        log.info("SMTP诊断: {}:{} - {}", emailConfig.getSmtpHost(), emailConfig.getSmtpPort(),
                overallSuccess ? "成功" : "失败");
        return ApiResponse.success(message, result);
    }



}
