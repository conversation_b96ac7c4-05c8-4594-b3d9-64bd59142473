package com.fixguru.dto.request;

import com.fixguru.common.BaseRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 重置密码请求
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ResetPasswordRequest extends BaseRequest {

    /**
     * 新密码
     */
    @NotBlank(message = "New password is required")
    @Size(min = 8, max = 20, message = "Password must be 8-20 characters")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*(),.?\":{}|<>])[a-zA-Z\\d!@#$%^&*(),.?\":{}|<>]{8,20}$",
            message = "Requires uppercase, number & symbol")
    private String newPassword;

    /**
     * 确认新密码
     */
    @NotBlank(message = "Password confirmation is required")
    private String confirmPassword;
}
