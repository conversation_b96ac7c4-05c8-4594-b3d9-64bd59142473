package com.fixguru.dto.request;

import com.fixguru.common.BaseRequest;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户注册请求
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserCreateRequest extends BaseRequest {

    /**
     * 用户名
     */
    @NotBlank(message = "User name is required")
    @Size(min = 3, max = 20, message = "User name: 3-20 characters")
    private String userName;

    /**
     * 邮箱
     */
    @NotBlank(message = "Email is required")
    @Email(message = "Invalid email format")
    private String email;

    /**
     * 密码
     */
    @NotBlank(message = "Password is required")
    @Size(min = 8, max = 20, message = "Password must be 8-20 characters")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*(),.?\":{}|<>])[a-zA-Z\\d!@#$%^&*(),.?\":{}|<>]{8,20}$",
            message = "Requires uppercase, number & symbol")
    private String password;

    /**
     * 同意服务条款和隐私政策
     */
    @NotNull(message = "Please agree to User Agreement")
    private Boolean agreeToTerms;

    /**
     * 订阅产品更新（可选）
     */
    private Boolean subscribeUpdates = false;
}
