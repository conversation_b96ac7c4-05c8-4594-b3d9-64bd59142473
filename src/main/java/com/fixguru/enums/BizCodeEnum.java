package com.fixguru.enums;

import lombok.Getter;

/**
 * 统一业务响应码枚举
 * 包含成功码、错误码和对应的英文消息
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
public enum BizCodeEnum {

    // ========== 成功响应码 (0xxx) ==========
    SUCCESS("0000", "Operation successful"),
    REGISTRATION_SUCCESS("0001", "Registration successful"),
    LOGIN_SUCCESS("0002", "Login successful"),
    LOGOUT_SUCCESS("0003", "Logout successful"),
    PASSWORD_RESET_EMAIL_SENT("0004", "If this email is registered, you will receive a password reset email"),

    ACCOUNT_UNLOCKED("0006", "Account unlocked successfully"),
    TOKEN_REFRESHED("0007", "Token refreshed successfully"),
    TOKEN_REVOKED("0008", "Token revoked successfully"),
    FILE_UPLOADED("0009", "File uploaded successfully"),
    FILE_DELETED("0010", "File deleted successfully"),
    DATA_SAVED("0011", "Data saved successfully"),
    DATA_UPDATED("0012", "Data updated successfully"),
    DATA_DELETED("0013", "Data deleted successfully"),

    // ========== 系统级错误 (1xxx) ==========
    SYSTEM_ERROR("1000", "Internal server error. Please try again later."),
    SERVICE_UNAVAILABLE("1001", "Service temporarily unavailable. Please try again later."),
    RATE_LIMIT_EXCEEDED("1002", "Too many attempts. Try later."),
    INVALID_REQUEST("1003", "Invalid request format."),
    INVALID_PARAMETER("1004", "Invalid request parameters."),
    NETWORK_ERROR("1005", "Network connection failed"),
    RESOURCE_NOT_FOUND("1006", "Resource not found"),

    // ========== 用户相关错误 (2xxx) ==========
    USER_NOT_FOUND("2001", "User not found or password incorrect"),
    USER_ALREADY_EXISTS("2002", "User already exists"),
    INVALID_CREDENTIALS("2003", "User not found or password incorrect"),
    USER_DISABLED("2004", "Account has been disabled. Please contact support."),
    USER_LOCKED("2005", "Account has been locked. Please try again later or contact support."),

    WEAK_PASSWORD("2008", "Password must be 8+ characters"),
    PASSWORD_MISMATCH("2009", "Passwords don't match"),
    EMAIL_ALREADY_EXISTS("2010", "This email is already registered"),
    PHONE_ALREADY_EXISTS("2011", "Phone number already registered"),
    TERMS_NOT_AGREED("2012", "Please agree to User Agreement"),
    USERNAME_TAKEN("2013", "User name taken"),
    USERNAME_INVALID_CHARS("2014", "Invalid characters in userName"),
    USERNAME_LENGTH_INVALID("2015", "User name: 3-20 characters"),

    // ========== 认证相关错误 (3xxx) ==========
    TOKEN_INVALID("3001", "Invalid access token"),
    TOKEN_EXPIRED("3002", "Access token has expired"),
    REFRESH_TOKEN_INVALID("3003", "Invalid or expired refresh token"),
    REFRESH_TOKEN_EXPIRED("3004", "Refresh token has expired"),
    UNAUTHORIZED("3005", "Authentication required"),
    FORBIDDEN("3006", "Access restricted. Contact support."),
    CAPTCHA_INVALID("3007", "Invalid/expired verification code"),
    CAPTCHA_EXPIRED("3008", "Verification code has expired"),
    LOGIN_FAILED("3009", "Login failed"),
    LOGOUT_FAILED("3010", "Logout failed"),
    RESET_TOKEN_INVALID("3011", "Invalid or expired reset token"),
    RESET_TOKEN_EXPIRED("3012", "Reset token has expired"),
    EMAIL_SEND_FAILED("3013", "Failed to send email"),
    ACTIVATE_TOKEN_INVALID("3014", "Invalid activate token"),
    ACTIVATE_TOKEN_EXPIRED("3015", "Activate token has expired"),
    USER_ALREADY_ACTIVATED("3016", "User account is already activated"),
    ACTIVATE_FAILED("3017", "Account activate failed"),
    EMAIL_SEND_RATE_LIMITED("3014", "Please wait before requesting another reset email"),
    EMAIL_DAILY_LIMIT_EXCEEDED("3015", "Daily email limit exceeded, please try again tomorrow"),

    // ========== 文件相关错误 (4xxx) ==========
    FILE_NOT_FOUND("4001", "File not found"),
    FILE_TOO_LARGE("4002", "File size exceeds limit"),
    INVALID_FILE_TYPE("4003", "Unsupported file format"),
    UPLOAD_FAILED("4004", "File upload failed"),
    DELETE_FAILED("4005", "File deletion failed"),
    STORAGE_ERROR("4006", "Storage service error"),

    // ========== 验证相关错误 (5xxx) ==========
    EMAIL_REQUIRED("5001", "Email is required"),
    EMAIL_INVALID_FORMAT("5002", "Invalid email format"),
    EMAIL_DOMAIN_NOT_SUPPORTED("5003", "Email domain not supported"),
    EMAIL_EDUCATIONAL_REQUIRED("5004", "Please use an educational email"),
    PASSWORD_REQUIRED("5005", "Password is required"),
    PASSWORD_TOO_SHORT("5006", "Password must be 8+ characters"),
    PASSWORD_COMPLEXITY_REQUIRED("5007", "Requires uppercase, number & symbol"),
    PASSWORD_CANNOT_MATCH_EMAIL("5008", "Password cannot match email"),
    FIRST_NAME_REQUIRED("5009", "First name is required"),
    FIRST_NAME_LENGTH_INVALID("5010", "First name: 1-50 characters"),
    REQUIRED_FIELDS_MISSING("5011", "Complete required fields"),
    TERMS_AGREEMENT_REQUIRED("5012", "Please agree to User Agreement"),

    // ========== 数据库相关错误 (6xxx) ==========
    DATABASE_ERROR("6001", "Database operation failed"),
    QUERY_ERROR("6002", "Database query failed"),
    UPDATE_ERROR("6003", "Database update failed"),
    DELETE_ERROR("6004", "Database delete failed"),
    ITEM_NOT_FOUND("6005", "Item not found in database");

    /**
     * 响应码
     */
    private final String code;

    /**
     * 英文消息（面向用户）
     */
    private final String message;

    BizCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据响应码查找枚举
     *
     * @param code 响应码
     * @return BizCodeEnum枚举，如果未找到返回SYSTEM_ERROR
     */
    public static BizCodeEnum fromCode(String code) {
        for (BizCodeEnum bizCodeEnum : values()) {
            if (bizCodeEnum.getCode().equals(code)) {
                return bizCodeEnum;
            }
        }
        return SYSTEM_ERROR;
    }

    /**
     * 判断是否为成功响应码
     *
     * @return 是否为成功响应码
     */
    public boolean isSuccess() {
        return code.startsWith("0");
    }

    /**
     * 判断是否为错误响应码
     *
     * @return 是否为错误响应码
     */
    public boolean isError() {
        return !isSuccess();
    }

    /**
     * 获取格式化的响应信息
     *
     * @return 格式化字符串：[响应码] 响应消息
     */
    public String getFormattedMessage() {
        return String.format("[%s] %s", code, message);
    }

    @Override
    public String toString() {
        return getFormattedMessage();
    }
}
