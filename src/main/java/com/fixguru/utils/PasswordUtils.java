package com.fixguru.utils;

import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 密码工具类
 * 提供密码加密和验证功能
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public final class PasswordUtils {

    private static final String ALGORITHM = "SHA-256";
    private static final int SALT_LENGTH = 16;
    private static final SecureRandom RANDOM = new SecureRandom();

    private PasswordUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 生成盐值
     *
     * @return Base64编码的盐值
     */
    public static String generateSalt() {
        byte[] salt = new byte[SALT_LENGTH];
        RANDOM.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }

    /**
     * 加密密码
     *
     * @param password 原始密码
     * @param salt     盐值
     * @return 加密后的密码
     */
    public static String hashPassword(String password, String salt) {
        try {
            MessageDigest digest = MessageDigest.getInstance(ALGORITHM);
            digest.update(Base64.getDecoder().decode(salt));
            byte[] hashedPassword = digest.digest(password.getBytes());
            return Base64.getEncoder().encodeToString(hashedPassword);
        } catch (NoSuchAlgorithmException e) {
            log.error("密码加密失败: {}", e.getMessage(), e);
            throw new RuntimeException("密码加密失败", e);
        }
    }

    /**
     * 加密密码（自动生成盐值）
     *
     * @param password 原始密码
     * @return 格式为 "salt:hashedPassword" 的字符串
     */
    public static String hashPassword(String password) {
        String salt = generateSalt();
        String hashedPassword = hashPassword(password, salt);
        return salt + ":" + hashedPassword;
    }

    /**
     * 验证密码
     *
     * @param password       原始密码
     * @param hashedPassword 存储的加密密码（格式：salt:hashedPassword）
     * @return 是否匹配
     */
    public static boolean verifyPassword(String password, String hashedPassword) {
        try {
            String[] parts = hashedPassword.split(":");
            if (parts.length != 2) {
                log.warn("密码格式不正确: {}", hashedPassword);
                return false;
            }

            String salt = parts[0];
            String storedHash = parts[1];
            String computedHash = hashPassword(password, salt);

            return storedHash.equals(computedHash);
        } catch (Exception e) {
            log.error("密码验证失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查密码强度
     * 要求：8-20位，包含大小写字母、数字和特殊符号
     *
     * @param password 密码
     * @return 是否符合强度要求
     */
    public static boolean isStrongPassword(String password) {
        if (password == null || password.length() < 8 || password.length() > 20) {
            return false;
        }

        boolean hasLower = password.chars().anyMatch(Character::isLowerCase);
        boolean hasUpper = password.chars().anyMatch(Character::isUpperCase);
        boolean hasDigit = password.chars().anyMatch(Character::isDigit);
        boolean hasSpecial = password.chars().anyMatch(c -> "!@#$%^&*(),.?\":{}|<>".indexOf(c) >= 0);

        return hasLower && hasUpper && hasDigit && hasSpecial;
    }
}
